{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao", "problems_count": 2, "analysis_results": [{"class_name": "BaseModelDao", "line": [3, 13], "description": "未解析的引用 BaseModelDao", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.BaseModelDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergy", "line": [4, 13, 27, 40, 54], "description": "未解析的引用 TeamGroupEnergy", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy"], "suggest": "请使用新的类路径替换"}]}