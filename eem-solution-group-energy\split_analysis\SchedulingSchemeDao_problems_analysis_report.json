{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao", "problems_count": 4, "analysis_results": [{"class_name": "ResultWithTotal", "line": [3, 24], "description": "未解析的引用 ResultWithTotal", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "BaseModelDao", "line": [4, 15], "description": "未解析的引用 BaseModelDao", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.BaseModelDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingScheme", "line": [5, 15, 24, 31, 39, 46], "description": "未解析的引用 SchedulingScheme", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingSchemeQueryDTO", "line": [6, 24], "description": "未解析的引用 SchedulingSchemeQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO"], "suggest": "请使用新的类路径替换"}]}