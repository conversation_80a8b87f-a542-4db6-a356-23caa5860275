# Java导入问题分析工具套件使用说明

## 概述

本工具套件用于分析和修复Java项目中的导入问题，特别是针对Qodana静态代码分析工具生成的`QodanaJavaSanity.xml`报告。工具套件包含两个主要工具，能够自动化地分析问题、查找正确的类路径并生成修复建议。

## 工具组成

### 1. `problem_splitter.py` - 问题分割器
**功能**: 将QodanaJavaSanity.xml中的问题按文件分组，生成独立的问题文件

### 2. `precise_class_finder.py` - 精确类查找器  
**功能**: 基于Maven classpath在实际JAR包中查找类的正确路径，生成修复建议

## 完整使用流程

### 步骤1: 分割问题文件

```bash
python problem_splitter.py <QodanaJavaSanity.xml文件路径>
```

**示例**:
```bash
python problem_splitter.py eem-solution-group-energy-core/QodanaJavaSanity.xml
```

**输出**:
- 在`split_problems/`目录下生成按文件分组的问题XML文件
- 文件命名格式: `<模块>_<包路径>_problems.xml`

**输出示例**:
```
split_problems/
├── eem-solution-group-energy-core_src_main_java_com_cet_eem_fusion_groupenergy_core_controller_TeamConfigController_problems.xml
├── eem-solution-group-energy-core_src_main_java_com_cet_eem_fusion_groupenergy_core_controller_TeamEnergyController_problems.xml
└── ...
```

### 步骤2: 分析类路径问题

```bash
python precise_class_finder.py <problems文件> <项目路径> [Maven可执行文件路径]
```

**参数说明**:
- `<problems文件>`: 步骤1生成的单个问题文件路径
- `<项目路径>`: Maven项目根目录路径
- `[Maven可执行文件路径]`: 可选，Maven可执行文件的完整路径

**示例**:
```bash
# 使用系统PATH中的Maven
python precise_class_finder.py "split_problems/eem-solution-group-energy-core_src_main_java_com_cet_eem_fusion_groupenergy_core_controller_TeamConfigController_problems.xml" "eem-solution-group-energy-core"

# 指定Maven路径
python precise_class_finder.py "split_problems/eem-solution-group-energy-core_src_main_java_com_cet_eem_fusion_groupenergy_core_controller_TeamConfigController_problems.xml" "eem-solution-group-energy-core" "E:\maven\apache-maven-3.6.3\bin\mvn.cmd"
```

**输出**:
- 控制台显示分析报告
- 在`split_analysis/`目录下生成JSON格式的分析报告
- 文件命名格式: `<原问题文件名>_analysis_report.json`

## 输出文件结构

### 问题分割输出 (`split_problems/`)
```xml
<problems>
  <problem>
    <file>file://$PROJECT_DIR$/path/to/JavaFile.java</file>
    <line>3</line>
    <module>module-name</module>
    <package>com.example.package</package>
    <entry_point TYPE="file" FQNAME="..." />
    <problem_class id="QodanaJavaSanity" severity="ERROR">Java 健全性</problem_class>
    <description>未解析的引用 ClassName</description>
    <highlighted_element>ClassName</highlighted_element>
    <language>JAVA</language>
    <offset>19</offset>
    <length>9</length>
  </problem>
</problems>
```

### 分析报告输出 (`split_analysis/`)
```json
{
  "file": "file://$PROJECT_DIR$/path/to/JavaFile.java",
  "module": "module-name",
  "package": "com.example.package",
  "problems_count": 7,
  "analysis_results": [
    {
      "class_name": "ClassName",
      "line": 3,
      "description": "未解析的引用 ClassName",
      "matched_class_paths": [
        "com.cet.electric.correct.package.ClassName"
      ],
      "suggest": "请使用新的类路径替换"
    },
    {
      "class_name": "ObsoleteClass",
      "line": 5,
      "description": "未解析的引用 ObsoleteClass",
      "matched_class_paths": [],
      "suggest": "类已经废弃"
    }
  ]
}
```

## 分析结果说明

### 建议类型
- **"请使用新的类路径替换"**: 找到了正确的类路径，需要更新import语句
- **"类已经废弃"**: 在com.cet范围内未找到该类，可能已被废弃或重命名

### 类路径过滤规则
- 只显示以`com.cet`开头的类路径
- 过滤掉第三方库的类路径（如hutool、mybatis等）
- 专注于项目内部的类路径问题

## 实际修复示例

基于分析报告修复Java文件中的import语句：

**修复前**:
```java
import com.cet.eem.auth.aspect.EnumAndOr;  // 错误路径
import com.cet.eem.auth.aspect.OperationPermission;  // 错误路径
```

**修复后**:
```java
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
```

## 批量处理脚本示例

处理多个问题文件的批量脚本：

### Windows批处理 (batch_analyze.bat)
```batch
@echo off
echo 开始批量分析Java导入问题...

REM 设置Maven路径
set MAVEN_PATH=E:\maven\apache-maven-3.6.3\bin\mvn.cmd

REM 分析split_problems目录下的所有问题文件
for %%f in (split_problems\*_problems.xml) do (
    echo 正在分析: %%f
    python precise_class_finder.py "%%f" "eem-solution-group-energy-core" "%MAVEN_PATH%"
    echo.
)

echo 批量分析完成！
pause
```

### Linux/Mac脚本 (batch_analyze.sh)
```bash
#!/bin/bash
echo "开始批量分析Java导入问题..."

# 设置Maven路径（如果需要）
MAVEN_PATH="mvn"

# 分析split_problems目录下的所有问题文件
for file in split_problems/*_problems.xml; do
    if [ -f "$file" ]; then
        echo "正在分析: $file"
        python3 precise_class_finder.py "$file" "eem-solution-group-energy-core" "$MAVEN_PATH"
        echo
    fi
done

echo "批量分析完成！"
```

## 环境要求

### 必需软件
- **Python 3.6+**: 运行分析工具
- **Maven 3.x**: 构建项目classpath
- **Java项目**: 包含pom.xml的Maven项目

### Python依赖
工具使用Python标准库，无需额外安装依赖包：
- `xml.etree.ElementTree`: XML解析
- `subprocess`: 执行Maven命令
- `zipfile`: 搜索JAR包中的类
- `json`: 生成JSON报告
- `pathlib`: 路径处理

### Maven配置要求
- Maven已正确安装并配置
- 项目能够正常执行`mvn dependency:build-classpath`命令
- 网络连接正常，能够下载依赖

## 故障排除

### 常见问题及解决方案

#### 1. Maven命令执行失败
**错误**: `系统找不到指定的文件`
**解决**: 
- 确认Maven已安装并在PATH中
- 或者使用完整路径指定Maven可执行文件

#### 2. classpath.txt文件未生成
**错误**: `classpath.txt文件未生成`
**解决**:
- 检查项目pom.xml配置是否正确
- 确认网络连接，能够下载Maven依赖
- 尝试手动运行: `mvn dependency:build-classpath`

#### 3. 找不到任何类
**现象**: 所有类都显示"未找到"
**解决**:
- 确认项目依赖配置正确
- 检查Maven本地仓库是否包含相关依赖
- 验证JAR包是否损坏

#### 4. 编码问题
**错误**: `multi-byte encodings are not supported`
**解决**: 
- 确保XML文件使用UTF-8编码
- 检查文件是否包含特殊字符

## 工具特性

### 优势
- **精确性**: 基于实际JAR包内容查找类，而非猜测
- **自动化**: 一键分析整个项目的导入问题
- **过滤性**: 只关注项目相关的类路径（com.cet开头）
- **结构化**: 生成JSON格式报告，便于后续处理

### 限制
- 仅支持Maven项目
- 需要网络连接下载依赖
- 只分析com.cet开头的类路径
- 依赖Maven命令行工具

## 版本历史

- **v1.0**: 基础问题分割功能
- **v1.1**: 添加精确类查找功能
- **v1.2**: 优化输出格式，添加JSON报告
- **v1.3**: 添加problems_count字段，分离输出目录

## 技术支持

如遇到问题，请检查：
1. Python和Maven版本是否符合要求
2. 项目配置是否正确
3. 网络连接是否正常
4. 参考故障排除章节

---

*本工具套件专为Java项目导入问题分析设计，提供自动化的问题诊断和修复建议。*