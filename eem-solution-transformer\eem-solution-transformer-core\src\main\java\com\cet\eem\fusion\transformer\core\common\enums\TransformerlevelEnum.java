package com.cet.eem.fusion.transformer.core.common.enums;


import java.util.Objects;
import java.util.stream.Stream;

/**
 * 变压器等级
 *
 * <AUTHOR>
 * @version 1.0
 * @created 16-3月-2022 9:37:28
 */
public enum TransformerlevelEnum {
    /**
     * 主变
     */
    HIGHTRANSFORMER(1, "主变"),
    MIDTRANSFORMER(2, "中压配变"),
    LOWTRANSFORMER(3, "低压配变");
     Integer type;
     String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    TransformerlevelEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
    public static TransformerlevelEnum getEnum(Integer type){
        return Stream.of(TransformerlevelEnum.values()).filter(item -> Objects.equals(item.getType(), type)).findFirst()
               .orElse(null);
    }
}
