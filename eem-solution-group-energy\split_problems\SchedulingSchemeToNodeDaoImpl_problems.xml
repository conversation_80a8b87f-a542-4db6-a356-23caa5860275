<?xml version='1.0' encoding='cp936'?>
<problems><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 conditions</description>
  <highlighted_element>conditions</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>10</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 query</description>
  <highlighted_element>query</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 LambdaQueryWrapper</description>
  <highlighted_element>LambdaQueryWrapper</highlighted_element>
  <language>JAVA</language>
  <offset>36</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ModelDaoImpl</description>
  <highlighted_element>ModelDaoImpl</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>12</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ModelDaoImpl</description>
  <highlighted_element>ModelDaoImpl</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>12</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>99</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>26</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl java.util.List&lt;SchedulingSchemeToNode&gt; queryBySchedulingSchemeId(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>27</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl java.util.List&lt;SchedulingSchemeToNode&gt; queryBySchedulingSchemeId(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 LambdaQueryWrapper</description>
  <highlighted_element>LambdaQueryWrapper</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>27</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl java.util.List&lt;SchedulingSchemeToNode&gt; queryBySchedulingSchemeId(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>27</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl java.util.List&lt;SchedulingSchemeToNode&gt; queryBySchedulingSchemeId(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>83</offset>
  <length>22</length>
</problem>
</problems>