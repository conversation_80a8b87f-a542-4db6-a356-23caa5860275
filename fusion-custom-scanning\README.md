# Java代码综合扫描分析工具

这是一个基于Python 3.12的Java代码综合扫描分析工具，集成了结构分析、全局匹配等多种检查功能，使用javalang库来解析Java代码并提取详细的结构信息。

## 项目结构

```
fusion-custom-scanning/
├── main.py                 # 主程序入口
├── test_checkers.py        # 功能测试脚本
├── requirements.txt        # Python依赖文件
├── README.md              # 说明文档
├── rules/                 # 配置规则文件夹
│   ├── structure_rules.json      # 结构检查规则
│   └── global_match_rules.json   # 全局匹配规则
├── service/               # 服务模块文件夹
│   ├── __init__.py
│   ├── structure_checker.py      # 结构问题检查器
│   ├── global_matcher.py         # 全局匹配检查器
│   ├── import_issues_processor.py # 导入问题处理器
│   ├── problem_splitter.py       # QodanaJavaSanity.xml问题拆分器
│   ├── precise_class_finder.py   # 精确类查找和分析器
│   ├── report_exporter.py        # 报告导出器
│   └── issues_report_generator.py # 问题报告生成器
├── QodanaJavaSanity.xml   # Qodana静态分析结果文件
└── output/                # 输出文件夹
    ├── scan_results_summary.json     # 汇总报告
    ├── scan_results_global.json      # 全局匹配报告
    ├── scan_results_structure.json   # 结构问题报告
    ├── scan_results_import.json      # 导入问题报告
    ├── scan_results_issues_report.md # 问题报告（Markdown格式）
    ├── split_problems/               # 拆分后的问题文件目录
    │   └── *_problems.xml            # 按类名拆分的问题文件
    └── split_analysis/               # 导入问题分析目录
        └── *_problems_analysis_report.json  # 按类名命名的导入问题分析报告
```

## 功能特性

### 1. 文件扫描
- 递归扫描指定目录下的所有Java文件
- 支持自定义扫描路径或使用默认路径
- 自动创建输出目录

### 2. 结构信息提取
- **类信息 (Classes)**
  - 类名、修饰符、继承关系、实现的接口
  - 类级别的注解信息
  - 开始行号和结束行号
  - 字段（属性）详细信息
  - 方法详细信息
  - 构造函数详细信息

- **接口信息 (Interfaces)**
  - 接口名、修饰符、继承关系
  - 接口级别的注解信息
  - 开始行号和结束行号
  - 方法签名信息

- **枚举信息 (Enums)**
  - 枚举名、修饰符、实现的接口
  - 枚举级别的注解信息
  - 开始行号和结束行号
  - 枚举常量（包含注解）
  - 枚举方法

- **字段信息 (Fields)**
  - 字段名、类型、修饰符
  - 字段级别的注解信息
  - 开始行号和结束行号

- **方法信息 (Methods)**
  - 方法名、返回类型、修饰符
  - 方法级别的注解信息
  - 参数列表（名称、类型、修饰符）
  - 异常声明
  - 开始行号和结束行号

- **构造函数信息 (Constructors)**
  - 构造函数名、修饰符
  - 构造函数级别的注解信息
  - 参数列表（名称、类型、修饰符）
  - 异常声明
  - 开始行号和结束行号

### 3. 全局匹配检查
- 基于关键字在项目所有文件中进行搜索匹配
- 支持多种文件类型（.java, .xml, .properties, .yml, .json等）
- 保证大小写一致的匹配
- 返回匹配的行数和内容
- 支持自定义关键字搜索

### 4. 其他问题检查（结构检查）
- 命名规范检查（类名、方法名、变量名、常量名）
- 代码质量检查（方法长度、类长度、参数数量）
- 安全问题检查（硬编码密码、SQL注入风险）
- 最佳实践检查（TODO注释、异常处理）

### 5. 导入问题检查
- **步骤5.1**: 拆分QodanaJavaSanity.xml文件，按类分组生成问题文件
- **步骤5.2**: 基于Maven依赖分析，生成精确的类路径建议
- **步骤5.3**: 处理分析结果，生成标准化的导入问题报告
- 自动识别未解析的import引用
- 提供正确的类路径建议
- 检测废弃的类和服务
- 支持多个匹配路径的智能建议
- 按严重程度分类（高优先级、警告、信息）

### 6. 注解信息提取
- 支持提取类、接口、枚举、字段、方法、构造函数、枚举常量的注解
- 提取注解名称和参数信息
- 支持单参数和多参数注解
- Spring注解特殊处理

### 7. 多格式报告导出
- JSON格式汇总报告
- JSON格式分类报告（全局匹配、结构问题、导入问题）
- Markdown格式问题报告（人类可读）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
# 使用默认目录和配置进行完整扫描
python main.py -v

# 指定目录扫描
python main.py /path/to/java/project -v

# 指定输出文件
python main.py -o output/my_results.json -v

# 指定QodanaJavaSanity.xml文件路径
python main.py --qodana-file /path/to/QodanaJavaSanity.xml -v
```

### 高级用法

```bash
# 只运行全局匹配检查
python main.py --skip-structure -v

# 只运行结构检查
python main.py --skip-global -v

# 跳过导入问题检查
python main.py --skip-import -v

# 导出所有格式的报告
python main.py --export-format json -v

# 组合使用所有参数
python main.py /path/to/java/project -o output/results.json --structure-rules rules/my_structure.json --global-rules rules/my_global.json -v
```

### 执行流程

程序按以下步骤执行：

1. **步骤1**: 扫描目录，收集所有Java文件
2. **步骤2**: 文件结构分析，提取类、属性、方法等信息
3. **步骤3**: 全局匹配检查，基于关键字搜索所有文件
4. **步骤4**: 其他问题检查，基于Java代码结构检查
5. **步骤5**: 导入问题检查
   - **步骤5.1**: 拆分QodanaJavaSanity.xml文件，按类分组生成问题文件
   - **步骤5.2**: 基于Maven依赖分析，生成精确的类路径建议
   - **步骤5.3**: 处理分析结果，生成标准化的导入问题报告
6. **步骤6**: 问题汇总和输出，生成各种格式的报告

## 数据结构说明

### 文件结构信息格式

```json
{
  "file_path": "文件路径",
  "package": "包名",
  "imports": [
    {
      "path": "导入路径",
      "static": false,
      "wildcard": false
    }
  ],
  "classes": [
    {
      "name": "类名",
      "modifiers": ["public", "final"],
      "extends": "父类名",
      "implements": ["接口1", "接口2"],
      "annotations": [
        {
          "name": "注解名",
          "arguments": [
            {
              "name": "参数名",
              "value": "参数值"
            }
          ]
        }
      ],
      "start_line": 10,
      "end_line": 50,
      "fields": [
        {
          "name": "字段名",
          "type": "字段类型",
          "modifiers": ["private", "static"],
          "annotations": [...],
          "start_line": 12,
          "end_line": 12
        }
      ],
      "methods": [
        {
          "name": "方法名",
          "return_type": "返回类型",
          "modifiers": ["public"],
          "annotations": [...],
          "parameters": [
            {
              "name": "参数名",
              "type": "参数类型",
              "modifiers": ["final"]
            }
          ],
          "throws": ["异常类型"],
          "start_line": 15,
          "end_line": 25
        }
      ],
      "constructors": [
        {
          "name": "构造函数名",
          "modifiers": ["public"],
          "annotations": [...],
          "parameters": [...],
          "throws": [...],
          "start_line": 30,
          "end_line": 35
        }
      ]
    }
  ],
  "interfaces": [...],
  "enums": [...],
  "parse_error": null
}
```

### 注解信息格式

```json
{
  "name": "@Override",
  "arguments": []
}

{
  "name": "@RequestMapping",
  "arguments": [
    {
      "name": "value",
      "value": "/api/users"
    },
    {
      "name": "method",
      "value": "GET"
    }
  ]
}
```

## 配置文件说明

### rules/structure_rules.json - 结构检查规则

这个配置文件控制结构问题检查的行为，基于具体的业务规则对Java代码结构进行检查。包含以下主要配置项：

#### 1. 方法返回类型检查规则 (method_return_type_rules)

检查方法的返回类型是否符合规范，可以禁止使用特定的返回类型。

```json
{
  "method_return_type_rules": {
    "description": "方法返回类型检查规则",
    "enabled": true,
    "forbidden_return_types": [
      "Result",
      "com.example.Result",
      "BaseResult"
    ],
    "severity": "warning",
    "message_template": "方法 '{method_name}' 的返回类型 '{return_type}' 不被允许，建议使用其他返回类型"
  }
}
```

**配置说明：**
- `enabled`: 是否启用此规则
- `forbidden_return_types`: 禁止使用的返回类型列表
- `severity`: 问题严重程度 (error, warning, info)
- `message_template`: 错误消息模板

#### 2. 注解内容检查规则 (annotation_content_rules)

检查特定注解的内容是否包含必需的关键字，主要用于@RequestMapping等注解的规范检查。

```json
{
  "annotation_content_rules": {
    "description": "注解内容检查规则",
    "enabled": true,
    "RequestMapping_rules": {
      "description": "@RequestMapping注解value内容检查",
      "enabled": true,
      "required_keywords": ["PluginInfoDef"],
      "severity": "error",
      "message_template": "@RequestMapping 注解的 value 必须包含关键字 '{keywords}'",
      "check_class_annotations": true,
      "check_method_annotations": true
    }
  }
}
```

**配置说明：**
- `required_keywords`: 注解value中必须包含的关键字列表
- `check_class_annotations`: 是否检查类级别的注解
- `check_method_annotations`: 是否检查方法级别的注解

**支持的注解类型：**
- `RequestMapping_rules`: @RequestMapping注解检查
- `GetMapping_rules`: @GetMapping注解检查（可选）
- `PostMapping_rules`: @PostMapping注解检查（可选）

#### 3. 目标检测规则 (target_detection_rules)

基于类名和方法名的白名单检测，用于标记特定的类和方法。

```json
{
  "target_detection_rules": {
    "description": "目标检测规则 - 基于类名和方法名的白名单检测",
    "enabled": true,
    "detection_list": [
      {
        "class_name": "UnnaturalTimeServiceImpl",
        "methods": []
      },
      {
        "class_name": "TimeServiceController",
        "methods": ["processTime", "updateTime"]
      },
      {
        "class_name": "DataProcessService",
        "methods": ["handleData"]
      }
    ],
    "severity": "info",
    "message_template": "检测到目标类 '{class_name}' 的方法 '{method_name}' (行: {line})"
  }
}
```

**检测逻辑：**
- **方法列表为空**：标记该类的所有方法
- **方法列表不为空**：只标记指定的方法，其他方法不算错误
- **类不在检测名单中**：完全忽略该类

**使用场景：**
- 识别包含特定服务类型的文件
- 标记需要特别关注的类和方法
- 进行代码审查和重构时的目标定位

#### 4. 正则表达式模式匹配规则 (regex_pattern_rules)

基于正则表达式的代码模式检测，用于识别特定的代码结构和调用模式。

```json
{
  "regex_pattern_rules": {
    "description": "正则表达式模式匹配规则",
    "enabled": true,
    "patterns": [
      {
        "name": "modelServiceUtils_queryWithChildren",
        "description": "检测modelServiceUtils.queryWithChildren方法调用",
        "pattern": "modelServiceUtils\\.queryWithChildren\\([^)]*\\.class\\s*\\)",
        "severity": "warning",
        "suggest": "请使用新的查询方法替代modelServiceUtils.queryWithChildren",
        "problemtype": "deprecated_query_method",
        "message_template": "发现已废弃的方法调用: modelServiceUtils.queryWithChildren (行: {line})"
      },
      {
        "name": "custom_pattern_example",
        "description": "自定义模式示例",
        "pattern": "YourClass\\.deprecatedMethod\\([^)]*\\)",
        "severity": "error",
        "suggest": "请使用新的方法替代",
        "problemtype": "deprecated_method",
        "message_template": "发现问题模式: {pattern} (行: {line})"
      }
    ]
  }
}
```

**配置说明：**
- `enabled`: 是否启用正则匹配规则
- `patterns`: 正则匹配模式列表
  - `name`: 模式名称，用于标识
  - `description`: 模式描述
  - `pattern`: 正则表达式模式
  - `severity`: 问题严重程度 (error, warning, info)
  - `suggest`: 修复建议
  - `problemtype`: 问题类型分类
  - `message_template`: 错误消息模板

**支持的模板变量：**
- `{pattern}`: 模式名称
- `{line}`: 匹配的行号
- `{match}`: 匹配的具体内容

**特性：**
- 自动过滤注释中的匹配（单行注释 `//` 和多行注释 `/* */`）
- 支持复杂的正则表达式模式
- 逐行匹配，精确定位问题位置
- 支持自定义错误消息和修复建议

**使用场景：**
- 检测废弃的方法调用模式
- 识别不规范的代码结构
- 自定义业务规则检查
- 代码迁移和重构辅助

#### 5. 通用设置 (general_settings)

控制结构检查的通用行为设置。

```json
{
  "general_settings": {
    "case_sensitive": true,
    "include_inherited_methods": false,
    "include_private_methods": true,
    "include_protected_methods": true,
    "include_public_methods": true,
    "include_package_methods": true
  }
}
```

**配置说明：**
- `case_sensitive`: 是否区分大小写
- `include_inherited_methods`: 是否包含继承的方法
- `include_private_methods`: 是否包含私有方法
- `include_protected_methods`: 是否包含受保护方法
- `include_public_methods`: 是否包含公共方法
- `include_package_methods`: 是否包含包级别方法

#### 结构检查使用示例

##### 示例1：检查方法返回类型

假设你的代码中有以下方法：
```java
public class UserService {
    // 这个方法会被标记为问题
    public Result getUserData() {
        return new Result();
    }

    // 这个方法是正常的
    public UserDTO getUserInfo() {
        return new UserDTO();
    }
}
```

检查结果：
- `getUserData()` 方法会被标记为warning，因为使用了禁止的Result返回类型

##### 示例2：检查@RequestMapping注解

假设你的控制器代码：
```java
@RestController
@RequestMapping("/api/user")  // 缺少PluginInfoDef关键字，会被标记
public class UserController {

    @RequestMapping("/PluginInfoDef/getData")  // 包含关键字，正常
    public String getData() {
        return "data";
    }

    @RequestMapping("/getInfo")  // 缺少PluginInfoDef关键字，会被标记
    public String getInfo() {
        return "info";
    }
}
```

检查结果：
- 类级别的`@RequestMapping("/api/user")`会被标记为error
- 方法`getInfo()`的`@RequestMapping("/getInfo")`会被标记为error
- 方法`getData()`的注解正常，不会被标记

##### 示例3：目标检测

配置文件中的检测名单：
```json
{
  "detection_list": [
    {
      "class_name": "TimeServiceImpl",
      "methods": []  // 空数组，标记所有方法
    },
    {
      "class_name": "UserController",
      "methods": ["processUser", "updateUser"]  // 只标记指定方法
    }
  ]
}
```

对于以下代码：
```java
public class TimeServiceImpl {
    public void processTime() { }     // 会被标记
    public void updateTime() { }      // 会被标记
    public void deleteTime() { }      // 会被标记（所有方法都标记）
}

public class UserController {
    public void processUser() { }     // 会被标记（在指定列表中）
    public void updateUser() { }      // 会被标记（在指定列表中）
    public void deleteUser() { }      // 不会被标记（不在指定列表中）
}

public class OrderService {
    public void processOrder() { }    // 不会被标记（类不在检测名单中）
}
```

#### 配置最佳实践

1. **规则启用策略**
   - 根据项目需要启用相应的规则
   - 新项目建议启用所有规则
   - 老项目可以逐步启用规则

2. **严重程度设置**
   - `error`: 必须修复的问题
   - `warning`: 建议修复的问题
   - `info`: 信息性提示

3. **目标检测配置**
   - 定期更新检测名单
   - 根据重构需要调整目标类和方法
   - 使用空方法列表可以快速标记整个类

4. **消息模板自定义**
   - 使用清晰的错误消息
   - 包含足够的上下文信息
   - 提供修复建议

### rules/global_match_rules.json - 全局匹配规则

这个配置文件控制全局关键字匹配检查的行为，包含以下主要配置项：

#### 文件扫描配置
```json
{
  "file_extensions": [".java", ".xml", ".properties", ".yml", ".yaml", ".json", ".sql", ".txt", ".md"],
  "exclude_directories": ["target", "build", ".git", ".idea", "node_modules", "logs"],
  "exclude_files": ["*.class", "*.jar", "*.war"]
}
```

- **file_extensions**: 要扫描的文件扩展名列表
  - 支持Java源码文件 (.java)
  - 支持配置文件 (.xml, .properties, .yml, .yaml, .json)
  - 支持SQL文件 (.sql)
  - 支持文档文件 (.txt, .md)
  - 可根据需要添加其他文件类型

- **exclude_directories**: 排除的目录列表
  - 构建目录：target, build, dist
  - 版本控制：.git
  - IDE配置：.idea, .vscode
  - 依赖目录：node_modules
  - 日志目录：logs, temp, tmp

- **exclude_files**: 排除的文件模式
  - 编译文件：*.class, *.jar, *.war
  - 压缩文件：*.zip, *.tar.gz
  - 日志文件：*.log

#### 关键字分类配置

每个关键字类别包含以下属性：
- **severity**: 严重程度 (critical, high, medium, warning, low, info)
- **case_sensitive**: 是否大小写敏感 (true/false)
- **description**: 类别描述
- **keywords**: 关键字列表

#### 自定义配置示例

你可以根据项目需要添加自定义关键字类别：

```json
{
  "custom_business_rules": {
    "severity": "warning",
    "case_sensitive": true,
    "description": "业务规则相关",
    "keywords": [
      "业务逻辑待完善",
      "临时方案",
      "需要优化"
    ]
  },
  "framework_specific": {
    "severity": "info",
    "case_sensitive": true,
    "description": "框架特定检查",
    "keywords": [
      "@Autowired",
      "@Component",
      "@Service",
      "@Repository"
    ]
  }
}
```

#### 全局匹配工作原理

1. **文件发现阶段**
   - 递归扫描项目目录
   - 根据file_extensions过滤文件类型
   - 排除exclude_directories中的目录
   - 跳过exclude_files模式匹配的文件

2. **关键字匹配阶段**
   - 逐行读取文件内容
   - 对每行内容进行关键字匹配
   - 根据case_sensitive设置进行大小写匹配
   - 记录匹配的行号和内容

3. **结果收集阶段**
   - 收集所有匹配结果
   - 按类别和严重程度分类
   - 生成统计信息
   - 导出到指定格式

#### 匹配结果格式

每个匹配结果包含以下信息：
```json
{
  "file": "文件路径",
  "type": "global_match",
  "category": "关键字类别",
  "severity": "严重程度",
  "keyword": "匹配的关键字",
  "message": "问题描述",
  "line": "行号",
  "line_content": "行内容",
  "match_type": "匹配类型(exact/case_insensitive)"
}
```

#### 全局匹配配置示例

以下是一个完整的global_match_rules.json配置示例，展示了如何配置不同类型的关键字检查：

```json
{
  "file_extensions": [".java", ".xml", ".properties", ".yml", ".json", ".sql"],
  "exclude_directories": ["target", "build", ".git", ".idea", "logs"],
  "exclude_files": ["*.class", "*.jar", "*.war"],
  "keywords": {
    "custom_security": {
      "severity": "critical",
      "case_sensitive": true,
      "description": "自定义安全检查",
      "keywords": [
        "admin_password",
        "root_key",
        "master_secret",
        "default_token"
      ]
    },
    "business_logic": {
      "severity": "warning",
      "case_sensitive": false,
      "description": "业务逻辑相关",
      "keywords": [
        "临时处理",
        "待完善",
        "硬编码",
        "magic number"
      ]
    }
  }
}
```

## 输出文件说明

程序会在 `output/` 目录下生成以下报告文件：

- `scan_results_summary.json` - 汇总报告（包含所有问题的统计和分类）
- `scan_results_global.json` - 全局匹配问题报告
- `scan_results_structure.json` - 结构问题报告
- `scan_results_import.json` - 导入问题报告
- `scan_results_issues_report.md` - Markdown格式的问题报告

### 输出结果字段说明

每个检测到的问题都包含以下标准字段：

#### 基础字段
- `file`: 文件路径
- `type`: 检查类型 (`structure_check`, `global_match`, `idea_check`)
- `category`: 问题类别 (`regex_pattern_match`, `target_detection`, `import_issues`, 等)
- `severity`: 严重程度 (`error`, `warning`, `info`)
- `message`: 问题描述信息
- `line`: 问题起始行号
- `end_line`: 问题结束行号

#### 增强字段 (v1.2.0+)
- `suggest`: **修复建议** - 提供具体的修复建议和替代方案
- `problemtype`: **问题类型** - 标准化的问题分类，便于统计和批量处理

#### 问题类型分类 (problemtype)

工具支持以下标准问题类型：

| 问题类型 | 说明 | 示例 |
|----------|------|------|
| `deprecated_service` | 废弃的服务类 | `NodeService` |
| `deprecated_implementation` | 废弃的实现类 | `NodeServiceImpl` |
| `deprecated_auth` | 废弃的认证服务 | `EemCloudAuthService` |
| `deprecated_dao` | 废弃的数据访问层 | `QuantityObjectDao` |
| `deprecated_utils` | 废弃的工具类 | `GlobalInfoUtils` |
| `deprecated_annotation` | 废弃的注解 | `@Resource` |
| `deprecated_base_class` | 废弃的基础类 | `BaseEntity` |
| `deprecated_query_method` | 废弃的查询方法 | `queryWithChildren` |

#### 输出示例

```json
{
  "file": "src/main/java/com/example/Service.java",
  "type": "structure_check",
  "category": "regex_pattern_match",
  "severity": "warning",
  "message": "发现已废弃的方法调用: modelServiceUtils.queryWithChildren (行: 42)",
  "line": 42,
  "end_line": 42,
  "element": "code_pattern",
  "element_name": "modelServiceUtils_queryWithChildren",
  "class_name": "ExampleService",
  "pattern": "modelServiceUtils\\.queryWithChildren\\([^)]*\\.class\\s*\\)",
  "matched_text": "modelServiceUtils.queryWithChildren(Entity.class)",
  "suggest": "请使用新的查询方法替代modelServiceUtils.queryWithChildren",
  "problemtype": "deprecated_query_method",
  "rule_type": "regex_pattern_match"
}
```

## 扩展开发

### 添加新的检查规则

1. 在相应的检查器类中添加新的检查方法
2. 在规则配置文件中添加相应的配置项
3. 更新文档说明

### 自定义报告格式

可以在 `service/report_exporter.py` 中添加新的导出格式支持。

## 注意事项

- 确保Python版本为3.12或以上
- 扫描大型项目时可能需要较长时间
- 某些复杂的Java语法可能无法正确解析
- 结束行号的计算基于简单的大括号匹配，复杂情况可能不准确
- 建议在扫描前备份重要代码
- 首次运行会自动创建 `output/` 目录

## 全局匹配使用案例

### 案例1：检查硬编码密码

假设你的项目中有以下代码：
```java
// application.properties
database.password=admin123
api.key=sk-1234567890

// UserService.java
String password = "defaultPassword";
String token = "hardcoded_token_value";
```

使用全局匹配检查会发现：
- `database.password=admin123` (hardcoded_credentials, critical)
- `api.key=sk-1234567890` (hardcoded_credentials, critical)
- `password` (security_issues, high)
- `token` (security_issues, high)

### 案例2：检查调试代码

项目中的调试代码：
```java
System.out.println("Debug: user login with password: " + password);
console.log("API response:", response);
logger.debug("Processing user data");
```

会被检测为：
- `System.out.println` (debug_code, warning)
- `console.log` (debug_code, warning)
- `logger.debug` (debug_code, warning)

### 案例3：自定义关键字搜索

你可以通过编程方式使用自定义关键字：
```python
from service.global_matcher import GlobalMatcher

matcher = GlobalMatcher("rules/global_match_rules.json")
custom_keywords = ["业务逻辑", "临时方案", "待优化"]
results = matcher.search_custom_keywords("/path/to/project", custom_keywords, case_sensitive=False)
```

## 测试

```bash
# 运行完整功能测试
python test_checkers.py

# 验证项目设置
python verify_setup.py

# 运行主程序测试
python main.py -v --skip-global  # 只测试结构检查
```

测试将创建示例Java文件并展示所有检查功能的效果，生成的测试报告保存在 `output/` 目录中。

## 导入问题检查详细说明

### 功能概述

导入问题检查功能专门用于分析Java代码中的import导入问题，通过解析`split_analysis`目录中的JSON文件来识别未解析的引用并提供修复建议。

### 工作原理

1. **数据源**: 扫描`split_analysis`目录中的JSON文件（按类名命名）
2. **问题识别**: 解析每个文件的`analysis_results`部分
3. **智能建议**: 根据匹配的类路径数量提供不同类型的建议
4. **严重程度分类**: 自动根据问题类型确定优先级

### 支持的问题类型

#### 1. 单一路径匹配 (severity: warning)
```json
{
  "class_name": "ProjectUnitClassify",
  "line": 3,
  "description": "未解析的引用 ProjectUnitClassify",
  "matched_class_paths": [
    "com.cet.eem.fusion.common.def.base.ProjectUnitClassify"
  ],
  "suggest": "请使用新的类路径替换"
}
```
**生成建议**: `建议使用: import com.cet.eem.fusion.common.def.base.ProjectUnitClassify;`

#### 2. 多路径选择 (severity: info)
```json
{
  "class_name": "CommonUtils",
  "line": 5,
  "description": "未解析的引用 CommonUtils",
  "matched_class_paths": [
    "com.cet.eem.fusion.common.utils.CommonUtils",
    "com.cet.electric.matterhorn.devicedataservice.common.utils.CommonUtils"
  ],
  "suggest": "请使用新的类路径替换"
}
```
**生成建议**: `建议使用以下路径之一: com.cet.eem.fusion.common.utils.CommonUtils, com.cet.electric.matterhorn.devicedataservice.common.utils.CommonUtils`

#### 3. 废弃类检测 (severity: high)
```json
{
  "class_name": "UnitService",
  "line": 23,
  "description": "未解析的引用 UnitService",
  "matched_class_paths": [],
  "suggest": "类已经废弃"
}
```
**生成建议**: `该类已废弃，请寻找替代方案或移除相关代码`

### 配置选项

#### 命令行参数
- `--split-analysis`: 指定split_analysis目录路径 (默认: `output/split_analysis`)
- `--skip-import`: 跳过导入问题检查

#### 使用示例
```bash
# 使用默认split_analysis目录
python main.py -v

# 指定自定义split_analysis目录
python main.py --split-analysis /path/to/custom/split_analysis -v

# 跳过导入问题检查
python main.py --skip-import -v
```

### 输出格式

导入问题会被包含在以下输出文件中：

1. **scan_results_import.json** - 单独的导入问题报告
2. **scan_results_summary.json** - 包含导入问题的汇总报告
3. **scan_results_issues_report.md** - Markdown格式的问题报告

### split_analysis文件格式

每个JSON文件应按照`{类名}_problems_analysis_report.json`的格式命名，并包含以下结构：
```json
{
  "file": "file://$PROJECT_DIR$/module/src/main/java/com/example/Class.java",
  "module": "module-name",
  "package": "com.example",
  "problems_count": 3,
  "analysis_results": [
    {
      "class_name": "SomeClass",
      "line": 5,
      "description": "未解析的引用 SomeClass",
      "matched_class_paths": ["com.example.SomeClass"],
      "suggest": "请使用新的类路径替换"
    }
  ]
}
```

### 最佳实践

1. **目录组织**: 将split_analysis目录放在项目根目录下，便于版本控制
2. **文件命名**: 按照`{类名}_problems_analysis_report.json`格式命名JSON文件，便于识别和维护
3. **定期更新**: 随着代码重构更新split_analysis中的问题文件
4. **优先级处理**: 优先处理high级别的废弃类问题
5. **批量修复**: 利用生成的具体import建议进行批量代码修复
