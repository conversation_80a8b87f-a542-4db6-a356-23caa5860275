"""
Java代码结构问题检查器
基于Java代码结构进行问题检查，如命名规范、代码质量、安全问题等
"""
import json
import re
from typing import List, Dict, Any
from datetime import datetime


class StructureChecker:
    """Java代码结构问题检查器"""

    def __init__(self, rules_file: str = "structure_rules.json"):
        """
        初始化结构检查器
        
        Args:
            rules_file: 结构规则配置文件路径
        """
        self.rules_file = rules_file
        self.issues = []
        self.rules = self._load_rules()

    def _load_rules(self) -> Dict[str, Any]:
        """加载结构检查规则"""
        try:
            with open(self.rules_file, 'r', encoding='utf-8') as f:
                rules = json.load(f)
                return rules if rules else {}
        except (FileNotFoundError, json.JSONDecodeError):
            print(f"警告: 无法加载结构规则文件 {self.rules_file}，使用默认规则")
            return self._get_default_rules()

    def _get_default_rules(self) -> Dict[str, Any]:
        """获取默认结构检查规则"""
        return {
            "naming_conventions": {
                "class_name_pattern": "^[A-Z][a-zA-Z0-9]*$",
                "method_name_pattern": "^[a-z][a-zA-Z0-9]*$",
                "variable_name_pattern": "^[a-z][a-zA-Z0-9]*$",
                "constant_name_pattern": "^[A-Z][A-Z0-9_]*$"
            },
            "code_quality": {
                "max_method_length": 50,
                "max_class_length": 500,
                "max_parameter_count": 5,
                "check_empty_catch_blocks": True,
                "check_magic_numbers": True,
                "check_duplicate_code": True
            },
            "security": {
                "check_hardcoded_passwords": True,
                "check_sql_injection": True,
                "check_unsafe_random": True,
                "check_file_path_traversal": True
            },
            "best_practices": {
                "check_todo_comments": True,
                "check_deprecated_usage": True,
                "check_exception_handling": True,
                "require_javadoc_for_public_methods": False
            }
        }

    def check_file_structures(self, file_structures: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        动态问题检查
        Args:
            file_structures: 文件结构信息列表
        Returns:
            问题列表
        """
        print("开始结构问题检查...")
        self.issues = []

        for file_structure in file_structures:
            if file_structure.get("parse_error"):
                continue

            file_path = file_structure.get("file_path", "")
            print(f"动态问题检查: {file_path}")

            # 检查类结构问题
            for class_info in file_structure.get("classes", []):
                self.issues.extend(self._check_class_structure(class_info, file_path))

            # 检查接口结构问题
            for interface_info in file_structure.get("interfaces", []):
                self.issues.extend(self._check_interface_structure(interface_info, file_path))

            # 检查枚举结构问题
            for enum_info in file_structure.get("enums", []):
                self.issues.extend(self._check_enum_structure(enum_info, file_path))

        print(f"结构检查完成，发现 {len(self.issues)} 个问题")
        return self.issues

    def _check_class_structure(self, class_info: Dict[str, Any], file_path: str) -> List[Dict[str, Any]]:
        """检查类结构问题"""
        issues = []
        naming_rules = self.rules.get("naming_conventions", {})
        quality_rules = self.rules.get("code_quality", {})

        # 检查类名命名规范
        class_name = class_info.get("name", "")
        pattern = naming_rules.get("class_name_pattern", "^[A-Z][a-zA-Z0-9]*$")
        if not self._matches_pattern(class_name, pattern):
            issues.append({
                "file": file_path,
                "type": "naming_convention",
                "severity": "warning",
                "message": f"类名 '{class_name}' 不符合命名规范",
                "line": class_info.get("start_line", 0),
                "element": "class",
                "element_name": class_name
            })

        # 检查类长度
        max_class_length = quality_rules.get("max_class_length", 500)
        class_length = class_info.get("end_line", 0) - class_info.get("start_line", 0)
        if class_length > max_class_length:
            issues.append({
                "file": file_path,
                "type": "code_quality",
                "severity": "warning",
                "message": f"类 '{class_name}' 过长 ({class_length} 行，建议不超过 {max_class_length} 行)",
                "line": class_info.get("start_line", 0),
                "element": "class",
                "element_name": class_name
            })

        # 检查字段问题
        for field in class_info.get("fields", []):
            issues.extend(self._check_field_structure(field, file_path, class_name))

        # 检查方法问题
        for method in class_info.get("methods", []):
            issues.extend(self._check_method_structure(method, file_path, class_name))

        # 检查构造函数问题
        for constructor in class_info.get("constructors", []):
            issues.extend(self._check_constructor_structure(constructor, file_path, class_name))

        return issues

    def _check_field_structure(self, field_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """检查字段结构问题"""
        issues = []
        naming_rules = self.rules.get("naming_conventions", {})

        field_name = field_info.get("name", "")
        modifiers = field_info.get("modifiers", [])

        # 检查字段命名规范
        if "static" in modifiers and "final" in modifiers:
            # 常量命名检查
            pattern = naming_rules.get("constant_name_pattern", "^[A-Z][A-Z0-9_]*$")
            if not self._matches_pattern(field_name, pattern):
                issues.append({
                    "file": file_path,
                    "type": "naming_convention",
                    "severity": "warning",
                    "message": f"常量 '{field_name}' 不符合命名规范",
                    "line": field_info.get("start_line", 0),
                    "element": "field",
                    "element_name": field_name,
                    "class_name": class_name
                })
        else:
            # 普通字段命名检查
            pattern = naming_rules.get("variable_name_pattern", "^[a-z][a-zA-Z0-9]*$")
            if not self._matches_pattern(field_name, pattern):
                issues.append({
                    "file": file_path,
                    "type": "naming_convention",
                    "severity": "warning",
                    "message": f"字段 '{field_name}' 不符合命名规范",
                    "line": field_info.get("start_line", 0),
                    "element": "field",
                    "element_name": field_name,
                    "class_name": class_name
                })

        return issues

    def _check_method_structure(self, method_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """检查方法结构问题"""
        issues = []
        naming_rules = self.rules.get("naming_conventions", {})
        quality_rules = self.rules.get("code_quality", {})

        method_name = method_info.get("name", "")

        # 检查方法命名规范
        pattern = naming_rules.get("method_name_pattern", "^[a-z][a-zA-Z0-9]*$")
        if not self._matches_pattern(method_name, pattern):
            issues.append({
                "file": file_path,
                "type": "naming_convention",
                "severity": "warning",
                "message": f"方法 '{method_name}' 不符合命名规范",
                "line": method_info.get("start_line", 0),
                "element": "method",
                "element_name": method_name,
                "class_name": class_name
            })

        # 检查方法长度
        max_method_length = quality_rules.get("max_method_length", 50)
        method_length = method_info.get("end_line", 0) - method_info.get("start_line", 0)
        if method_length > max_method_length:
            issues.append({
                "file": file_path,
                "type": "code_quality",
                "severity": "warning",
                "message": f"方法 '{method_name}' 过长 ({method_length} 行，建议不超过 {max_method_length} 行)",
                "line": method_info.get("start_line", 0),
                "element": "method",
                "element_name": method_name,
                "class_name": class_name
            })

        # 检查参数数量
        max_param_count = quality_rules.get("max_parameter_count", 5)
        param_count = len(method_info.get("parameters", []))
        if param_count > max_param_count:
            issues.append({
                "file": file_path,
                "type": "code_quality",
                "severity": "warning",
                "message": f"方法 '{method_name}' 参数过多 ({param_count} 个，建议不超过 {max_param_count} 个)",
                "line": method_info.get("start_line", 0),
                "element": "method",
                "element_name": method_name,
                "class_name": class_name
            })

        return issues

    def _check_constructor_structure(self, constructor_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """检查构造函数结构问题"""
        issues = []
        quality_rules = self.rules.get("code_quality", {})

        constructor_name = constructor_info.get("name", "")

        # 检查构造函数参数数量
        max_param_count = quality_rules.get("max_parameter_count", 5)
        param_count = len(constructor_info.get("parameters", []))
        if param_count > max_param_count:
            issues.append({
                "file": file_path,
                "type": "code_quality",
                "severity": "warning",
                "message": f"构造函数 '{constructor_name}' 参数过多 ({param_count} 个，建议不超过 {max_param_count} 个)",
                "line": constructor_info.get("start_line", 0),
                "element": "constructor",
                "element_name": constructor_name,
                "class_name": class_name
            })

        return issues

    def _check_interface_structure(self, interface_info: Dict[str, Any], file_path: str) -> List[Dict[str, Any]]:
        """检查接口结构问题"""
        issues = []
        naming_rules = self.rules.get("naming_conventions", {})

        # 检查接口命名规范
        interface_name = interface_info.get("name", "")
        pattern = naming_rules.get("class_name_pattern", "^[A-Z][a-zA-Z0-9]*$")  # 接口使用类名规范
        if not self._matches_pattern(interface_name, pattern):
            issues.append({
                "file": file_path,
                "type": "naming_convention",
                "severity": "warning",
                "message": f"接口名 '{interface_name}' 不符合命名规范",
                "line": interface_info.get("start_line", 0),
                "element": "interface",
                "element_name": interface_name
            })

        return issues

    def _check_enum_structure(self, enum_info: Dict[str, Any], file_path: str) -> List[Dict[str, Any]]:
        """检查枚举结构问题"""
        issues = []
        naming_rules = self.rules.get("naming_conventions", {})

        # 检查枚举命名规范
        enum_name = enum_info.get("name", "")
        pattern = naming_rules.get("class_name_pattern", "^[A-Z][a-zA-Z0-9]*$")  # 枚举使用类名规范
        if not self._matches_pattern(enum_name, pattern):
            issues.append({
                "file": file_path,
                "type": "naming_convention",
                "severity": "warning",
                "message": f"枚举名 '{enum_name}' 不符合命名规范",
                "line": enum_info.get("start_line", 0),
                "element": "enum",
                "element_name": enum_name
            })

        return issues

    def _matches_pattern(self, text: str, pattern: str) -> bool:
        """检查文本是否匹配正则表达式模式"""
        try:
            return bool(re.match(pattern, text))
        except re.error:
            return True  # 如果正则表达式有误，跳过检查

    def export_issues(self, output_file: str = "structure_issues.json") -> None:
        """
        导出结构问题到文件
        
        Args:
            output_file: 输出文件路径
        """
        print(f"导出结构问题到文件: {output_file}")

        # 按严重程度分类统计
        severity_count = {}
        for issue in self.issues:
            severity = issue.get("severity", "unknown")
            severity_count[severity] = severity_count.get(severity, 0) + """
Java代码结构问题检查器
基于业务规则对Java代码结构进行检查，如方法返回类型、注解内容、特定属性等
"""


import json
import re
from typing import List, Dict, Any
from datetime import datetime


class StructureChecker:
    """Java代码结构问题检查器"""

    def __init__(self, rules_file: str = "rules/structure_rules.json"):
        """
        初始化结构检查器

        Args:
            rules_file: 结构规则配置文件路径
        """
        self.rules_file = rules_file
        self.issues = []
        self.rules = self._load_rules()

    def _load_rules(self) -> Dict[str, Any]:
        """加载结构检查规则"""
        try:
            with open(self.rules_file, 'r', encoding='utf-8') as f:
                rules = json.load(f)
                return rules if rules else {}
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"警告: 无法加载结构规则文件 {self.rules_file}: {e}")
            print("跳过结构检查")
            return {}

    def check_file_structures(self, file_structures: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        动态问题检查
        Args:
            file_structures: 文件结构信息列表
        Returns:
            问题列表
        """
        # 如果没有加载到规则，跳过检查
        if not self.rules:
            print("跳过结构问题检查（未加载到规则文件）")
            return []

        print("开始结构问题检查...")
        self.issues = []

        for file_structure in file_structures:
            if file_structure.get("parse_error"):
                continue

            file_path = file_structure.get("file_path", "")
            print(f"动态问题检查: {file_path}")

            # 检查类结构问题
            for class_info in file_structure.get("classes", []):
                self.issues.extend(self._check_class_structure(class_info, file_path))

            # 检查接口结构问题
            for interface_info in file_structure.get("interfaces", []):
                self.issues.extend(self._check_interface_structure(interface_info, file_path))

        print(f"结构检查完成，发现 {len(self.issues)} 个问题")
        return self.issues

    def _check_class_structure(self, class_info: Dict[str, Any], file_path: str) -> List[Dict[str, Any]]:
        """检查类结构问题"""
        issues = []
        class_name = class_info.get("name", "")

        # 1. 检查类注解（@RequestMapping规则）
        issues.extend(self._check_class_annotations(class_info, file_path, class_name))

        # 2. 检查方法返回类型和方法注解
        for method in class_info.get("methods", []):
            issues.extend(self._check_method_return_type(method, file_path, class_name))
            issues.extend(self._check_method_annotations(method, file_path, class_name))

        # 3. 检查目标检测规则（基于类名和方法名的白名单）
        issues.extend(self._check_target_detection(class_info, file_path, class_name))

        # 4. 检查正则表达式模式匹配规则
        issues.extend(self._check_regex_patterns(class_info, file_path, class_name))

        return issues

    def _check_interface_structure(self, interface_info: Dict[str, Any], file_path: str) -> List[Dict[str, Any]]:
        """检查接口结构问题"""
        issues = []
        interface_name = interface_info.get("name", "")

        # 检查接口方法返回类型
        for method in interface_info.get("methods", []):
            issues.extend(self._check_method_return_type(method, file_path, interface_name))
            issues.extend(self._check_method_annotations(method, file_path, interface_name))

        return issues

    def _check_method_return_type(self, method_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """
        检查方法返回类型规则
        场景1: 针对所有方法的返回类型，支持多个规则校验
        """
        issues = []
        return_type_rules_list = self.rules.get("method_return_type_rules", [])

        # 兼容旧格式：如果是字典，转换为列表
        if isinstance(return_type_rules_list, dict):
            return_type_rules_list = [return_type_rules_list]

        method_name = method_info.get("name", "")
        return_type = method_info.get("return_type", "")

        # 遍历每个返回类型规则
        for rule in return_type_rules_list:
            if not rule.get("enabled", True):
                continue

            forbidden_types = rule.get("forbidden_return_types", [])

            # 检查是否使用了禁止的返回类型
            for forbidden_type in forbidden_types:
                if return_type == forbidden_type or return_type.startswith(f"{forbidden_type}<"):
                    severity = rule.get("severity", "warning")
                    problemtype = rule.get("problemtype", "forbidden_return_type")
                    message_template = rule.get("message_template",
                                               "方法 '{method_name}' 的返回类型 '{return_type}' 不被允许")

                    issues.append({
                        "file": file_path,
                        "type": "structure_check",
                        "category": "method_return_type",
                        "severity": severity,
                        "problemtype": problemtype,
                        "message": message_template.format(
                            method_name=method_name,
                            return_type=return_type,
                            class_name=class_name
                        ),
                        "line": method_info.get("start_line", 0),
                        "end_line": method_info.get("end_line", 0),
                        "element": "method",
                        "element_name": method_name,
                        "class_name": class_name,
                        "return_type": return_type,
                        "rule_type": "forbidden_return_type"
                    })

        return issues

    def _check_class_annotations(self, class_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """
        检查类注解规则
        场景2: 针对注解的扫描，@RequestMapping的value内容需要包含PluginInfoDef关键字
        """
        issues = []
        annotation_rules = self.rules.get("annotation_content_rules", {})

        annotations = class_info.get("annotations", [])

        for annotation in annotations:
            annotation_name = annotation.get("name", "")

            # 检查@RequestMapping注解
            if annotation_name in ["@RequestMapping", "RequestMapping"]:
                # 使用注解的精确行号，如果没有则使用类的开始行
                annotation_line = annotation.get("line", class_info.get("start_line", 0))
                issues.extend(self._check_request_mapping_annotation(
                    annotation, file_path, class_name, "class",
                    annotation_line, annotation_rules
                ))

        return issues

    def _check_method_annotations(self, method_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """
        检查方法注解规则
        场景2: 针对方法的@RequestMapping注解检查
        """
        issues = []
        annotation_rules = self.rules.get("annotation_content_rules", {})

        method_name = method_info.get("name", "")
        annotations = method_info.get("annotations", [])

        for annotation in annotations:
            annotation_name = annotation.get("name", "")

            # 检查@RequestMapping注解
            if annotation_name in ["@RequestMapping", "RequestMapping"]:
                # 使用注解的精确行号，如果没有则使用方法的开始行
                annotation_line = annotation.get("line", method_info.get("start_line", 0))
                issues.extend(self._check_request_mapping_annotation(
                    annotation, file_path, class_name, "method",
                    annotation_line, annotation_rules, method_name
                ))

        return issues

    def _check_request_mapping_annotation(self, annotation: Dict[str, Any], file_path: str,
                                          class_name: str, element_type: str, line_number: int,
                                          annotation_rules: Dict[str, Any], method_name: str = None) -> List[
        Dict[str, Any]]:
        """
        检查@RequestMapping注解的value内容
        """
        issues = []
        request_mapping_rules = annotation_rules.get("RequestMapping_rules", {})
        required_keywords = request_mapping_rules.get("required_keywords", ["PluginInfoDef"])

        # 获取注解的value参数
        annotation_arguments = annotation.get("arguments", [])
        value_content = ""

        for arg in annotation_arguments:
            arg_name = arg.get("name", "")
            arg_value = arg.get("value", "")

            # 查找value参数或默认参数
            if arg_name == "value" or arg_name == "":
                value_content = str(arg_value)
                break

        # 检查是否包含必需的关键字
        missing_keywords = []
        for keyword in required_keywords:
            if keyword not in value_content:
                missing_keywords.append(keyword)

        if missing_keywords:
            severity = request_mapping_rules.get("severity", "error")
            message_template = request_mapping_rules.get("message_template",
                                                         "@RequestMapping 注解的 value 必须包含关键字 '{keywords}'")

            element_name = method_name if method_name else class_name
            element_desc = f"方法 '{method_name}'" if method_name else f"类 '{class_name}'"

            issues.append({
                "file": file_path,
                "type": "structure_check",
                "category": "annotation_content",
                "severity": severity,
                "message": f"{element_desc} 的 @RequestMapping 注解缺少必需关键字: {', '.join(missing_keywords)}",
                "line": line_number,
                "element": element_type,
                "element_name": element_name,
                "class_name": class_name,
                "annotation_name": "@RequestMapping",
                "annotation_value": value_content,
                "missing_keywords": missing_keywords,
                "rule_type": "annotation_content_check"
            })

        return issues

    def _check_target_detection(self, class_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """
        检查目标检测规则
        场景3: 基于属性类型的检测

        规则说明:
        - 检测类中是否有指定类型的属性（如OilFieldService）
        - 如果方法名为空，则标记找到该属性的类，并收集所有调用该属性的行号
        - 如果方法名不为空，则检查该属性对象是否调用了指定的方法
        """
        issues = []
        target_rules = self.rules.get("target_detection_rules", {})

        if not target_rules.get("enabled", True):
            return issues

        detection_list = target_rules.get("detection_list", [])
        severity = target_rules.get("severity", "info")

        # 获取类的所有属性
        class_fields = class_info.get("fields", [])

        # 遍历检测配置
        for config in detection_list:
            target_type = config.get("class_name")  # 这里是属性类型，如OilFieldService
            target_methods = config.get("methods", [])
            suggest = config.get("suggest", "")  # 获取建议信息
            problemtype = config.get("problemtype", "")  # 获取问题类型

            # 查找该类型的属性
            matching_fields = []
            for field in class_fields:
                field_type = field.get("type", "")
                field_name = field.get("name", "")

                # 检查属性类型是否匹配（精确匹配）
                if self._is_exact_type_match(field_type, target_type):
                    matching_fields.append({
                        "field_name": field_name,
                        "field_type": field_type,
                        "field_info": field
                    })

            # 如果找到了匹配的属性
            if matching_fields:
                for field_match in matching_fields:
                    field_name = field_match["field_name"]
                    field_type = field_match["field_type"]
                    field_info = field_match["field_info"]

                    # 收集所有相关的行号：声明行 + 调用行
                    all_lines = []
                    field_declaration_line = field_info.get("start_line", 0)
                    all_lines.append(field_declaration_line)

                    # 查找所有对该属性的调用行（不限于特定方法）
                    all_call_lines = self._find_all_field_usage_lines(class_info, field_name)
                    all_lines.extend(all_call_lines)

                    # 去重并排序
                    all_lines = sorted(list(set(all_lines)))

                    # 如果方法列表为空，直接标记属性并包含所有调用行
                    if not target_methods:
                        issues.append({
                            "file": file_path,
                            "type": "structure_check",
                            "category": "target_detection",
                            "severity": severity,
                            "message": f"匹配到类型 '{target_type}': {field_name} (类型: {field_type}) - 声明行: {field_declaration_line}, 调用行: {all_call_lines}",
                            "line": field_declaration_line,
                            "end_line": field_info.get("end_line", field_declaration_line),
                            "lines": all_lines,  # 新增：包含所有相关行号的数组
                            "declaration_line": field_declaration_line,  # 新增：声明行
                            "usage_lines": all_call_lines,  # 新增：使用行数组
                            "element": "field",
                            "element_name": field_name,
                            "class_name": class_name,
                            "property_type": field_type,
                            "target_type": target_type,
                            "suggest": suggest,  # 添加建议信息
                            "problemtype": problemtype,  # 添加问题类型
                            "rule_type": "target_property_detection"
                        })
                    else:
                        # 检查该属性对象是否调用了指定的方法
                        method_calls = self._find_method_calls_on_field(class_info, field_name, target_methods)

                        for method_call in method_calls:
                            # 使用实际调用行号在消息中显示，但line/end_line保持方法的开始/结束行
                            actual_call_line = method_call.get("actual_call_line", method_call["line"])
                            issues.append({
                                "file": file_path,
                                "type": "structure_check",
                                "category": "target_detection",
                                "severity": severity,
                                "message": f"检测到属性 '{field_name}' 调用目标方法 '{method_call['method_name']}' (调用行: {actual_call_line}, 方法: {method_call.get('calling_method', 'unknown')})",
                                "line": method_call["line"],  # 方法开始行
                                "end_line": method_call["end_line"],  # 方法结束行
                                "lines": all_lines,  # 新增：包含所有相关行号的数组
                                "declaration_line": field_declaration_line,  # 新增：声明行
                                "usage_lines": all_call_lines,  # 新增：使用行数组
                                "element": "method_call",
                                "element_name": method_call["method_name"],
                                "class_name": class_name,
                                "property_name": field_name,
                                "property_type": field_type,
                                "target_type": target_type,
                                "calling_method": method_call.get("calling_method"),
                                "actual_call_line": actual_call_line,  # 保存实际调用行号
                                "suggest": suggest,  # 添加建议信息
                                "problemtype": problemtype,  # 添加问题类型
                                "rule_type": "target_method_call_detection"
                            })

        return issues

    def _is_exact_type_match(self, field_type: str, target_type: str) -> bool:
        """
        检查字段类型是否与目标类型精确匹配

        Args:
            field_type: 字段的完整类型，如 "com.example.EemNodeService" 或 "NodeService"
            target_type: 目标类型，如 "NodeService"

        Returns:
            是否精确匹配
        """
        if not field_type or not target_type:
            return False

        # 1. 完全相等
        if field_type == target_type:
            return True

        # 2. 如果field_type是完全限定名，提取简单类名进行比较
        # 例如: "com.example.NodeService" -> "NodeService"
        if '.' in field_type:
            simple_class_name = field_type.split('.')[-1]
            if simple_class_name == target_type:
                return True

        # 3. 处理泛型类型
        # 例如: "List<NodeService>" -> "NodeService"
        import re
        generic_match = re.search(r'<([^<>]+)>$', field_type)
        if generic_match:
            generic_type = generic_match.group(1).strip()
            if self._is_exact_type_match(generic_type, target_type):
                return True

        # 4. 处理数组类型
        # 例如: "NodeService[]" -> "NodeService"
        if field_type.endswith('[]'):
            array_base_type = field_type[:-2]
            if self._is_exact_type_match(array_base_type, target_type):
                return True

        return False

    def _find_all_field_usage_lines(self, class_info: Dict[str, Any], field_name: str) -> List[int]:
        """
        查找类中所有使用指定属性的行号

        Args:
            class_info: 类信息
            field_name: 属性名（如oilFieldService）

        Returns:
            使用该属性的所有行号列表
        """
        usage_lines = []

        # 遍历类的所有方法
        for method in class_info.get("methods", []):
            method_body = method.get("body", "")
            start_line = method.get("start_line", 0)

            if not method_body:
                continue

            # 查找方法体中所有对该属性的使用
            lines = method_body.split('\n')
            for i, line in enumerate(lines):
                line_number = start_line + i
                # 移除注释和字符串字面量，避免误匹配
                cleaned_line = self._remove_comments_and_strings(line)

                # 检查是否包含对该属性的引用
                # 匹配模式：fieldName. 或 fieldName空格 或 fieldName)等
                import re
                pattern = rf'\b{re.escape(field_name)}\b'
                if re.search(pattern, cleaned_line):
                    # 进一步检查是否真的是属性调用（避免误匹配变量名相同的情况）
                    # 检查是否有 fieldName. 或 fieldName( 等模式
                    call_pattern = rf'\b{re.escape(field_name)}\s*[\.\(]'
                    if re.search(call_pattern, cleaned_line):
                        usage_lines.append(line_number)

        return usage_lines

    def _find_method_calls_on_field(self, class_info: Dict[str, Any], field_name: str, target_methods: List[str]) -> List[Dict[str, Any]]:
        """
        查找类中对指定属性调用指定方法的情况

        Args:
            class_info: 类信息
            field_name: 属性名（如oilFieldService）
            target_methods: 目标方法列表

        Returns:
            方法调用信息列表
        """
        method_calls = []

        # 遍历类的所有方法
        for method in class_info.get("methods", []):
            method_name = method.get("name", "")
            method_body = method.get("body", "")  # 方法体内容
            start_line = method.get("start_line", 0)
            end_line = method.get("end_line", 0)

            if not method_body:
                continue

            # 检查方法体中是否有对该属性的方法调用
            for target_method in target_methods:
                # 查找方法调用，支持跨行
                found_calls = self._find_cross_line_method_calls(
                    method_body, field_name, target_method, start_line
                )

                for call_info in found_calls:
                    method_calls.append({
                        "method_name": target_method,
                        "line": start_line,  # 方法开始行
                        "end_line": end_line,  # 方法结束行
                        "calling_method": method_name,
                        "call_pattern": call_info["pattern"],
                        "line_content": call_info["content"],
                        "actual_call_line": call_info["line"]  # 实际调用行号，用于message
                    })

        return method_calls

    def _find_cross_line_method_calls(self, method_body: str, field_name: str, target_method: str, start_line: int) -> List[Dict[str, Any]]:
        """
        查找跨行的方法调用

        Args:
            method_body: 方法体内容
            field_name: 属性名
            target_method: 目标方法名
            start_line: 方法开始行号

        Returns:
            方法调用信息列表
        """
        import re

        calls = []

        # 移除注释和字符串字面量，避免误匹配
        cleaned_body = self._remove_comments_and_strings(method_body)

        # 构建正则表达式，匹配跨行的方法调用
        # 匹配模式：fieldName \s* . \s* methodName \s* (
        pattern = rf'\b{re.escape(field_name)}\s*\.\s*{re.escape(target_method)}\s*\('

        # 查找所有匹配
        for match in re.finditer(pattern, cleaned_body, re.MULTILINE | re.DOTALL):
            match_start = match.start()
            match_end = match.end()

            # 计算匹配位置的行号
            lines_before_match = method_body[:match_start].count('\n')
            lines_in_match = method_body[match_start:match_end].count('\n')

            call_start_line = start_line + lines_before_match
            call_end_line = start_line + lines_before_match + lines_in_match

            # 提取匹配的内容用于显示
            match_content = method_body[match_start:match_end].strip()

            calls.append({
                "line": call_start_line,
                "end_line": call_end_line,
                "pattern": match.group(),
                "content": match_content
            })

        return calls

    def _remove_comments_and_strings(self, code: str) -> str:
        """
        移除代码中的注释和字符串字面量，避免在注释或字符串中误匹配

        Args:
            code: 源代码

        Returns:
            清理后的代码
        """
        import re

        # 简单的清理：移除单行注释、多行注释和字符串字面量
        # 注意：这是一个简化的实现，完整的解析需要更复杂的词法分析

        # 移除多行注释 /* ... */
        code = re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)

        # 移除单行注释 //
        code = re.sub(r'//.*?$', '', code, flags=re.MULTILINE)

        # 移除字符串字面量 "..." 和 '...'
        code = re.sub(r'"(?:[^"\\]|\\.)*"', '""', code)
        code = re.sub(r"'(?:[^'\\]|\\.)*'", "''", code)

        return code

    def export_issues(self, output_file: str = "output/structure_issues.json") -> None:
        """
        导出结构问题到文件

        Args:
            output_file: 输出文件路径
        """
        print(f"导出结构问题到文件: {output_file}")

        # 按严重程度分类统计
        severity_count = {}
        category_count = {}
        rule_type_count = {}

        for issue in self.issues:
            severity = issue.get("severity", "unknown")
            category = issue.get("category", "unknown")
            rule_type = issue.get("rule_type", "unknown")

            severity_count[severity] = severity_count.get(severity, 0) + 1
            category_count[category] = category_count.get(category, 0) + 1
            rule_type_count[rule_type] = rule_type_count.get(rule_type, 0) + 1

        # 构建输出数据
        output_data = {
            "scan_info": {
                "timestamp": datetime.now().isoformat(),
                "total_issues": len(self.issues),
                "severity_summary": severity_count,
                "category_summary": category_count,
                "rule_type_summary": rule_type_count,
                "scan_type": "structure_check"
            },
            "rules_applied": {
                "method_return_type_rules": self.rules.get("method_return_type_rules", {}),
                "annotation_content_rules": self.rules.get("annotation_content_rules", {}),
                "target_detection_rules": self.rules.get("target_detection_rules", {})
            },
            "issues": self.issues
        }

        # 写入JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"结构问题检查完成！结果已保存到 {output_file}")
        print(f"问题统计:")
        print(f"  - 发现问题数: {len(self.issues)}")

        if severity_count:
            print("  按严重程度:")
            for severity, count in severity_count.items():
                print(f"    - {severity}: {count}")

        if category_count:
            print("  按类别:")
            for category, count in category_count.items():
                print(f"    - {category}: {count}")

        if rule_type_count:
            print("  按规则类型:")
            for rule_type, count in rule_type_count.items():
                print(f"    - {rule_type}: {count}")

        # 构建输出数据
        output_data = {
            "scan_info": {
                "timestamp": datetime.now().isoformat(),
                "total_issues": len(self.issues),
                "severity_summary": severity_count,
                "scan_type": "structure_check"
            },
            "issues": self.issues
        }

        # 写入JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"结构问题检查完成！结果已保存到 {output_file}")
        print(f"问题统计:")
        print(f"  - 发现问题数: {len(self.issues)}")
        for severity, count in severity_count.items():
            print(f"  - {severity}: {count}")

    def _check_regex_patterns(self, class_info: Dict[str, Any], file_path: str, class_name: str) -> List[Dict[str, Any]]:
        """
        检查正则表达式模式匹配规则

        Args:
            class_info: 类信息
            file_path: 文件路径
            class_name: 类名

        Returns:
            问题列表
        """
        issues = []

        # 获取正则匹配规则
        regex_rules = self.rules.get("regex_pattern_rules", {})
        if not regex_rules.get("enabled", False):
            return issues

        patterns = regex_rules.get("patterns", [])
        if not patterns:
            return issues

        # 读取文件内容进行正则匹配
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
                lines = file_content.split('\n')
        except Exception as e:
            print(f"警告: 无法读取文件 {file_path}: {e}")
            return issues

        # 对每个正则模式进行匹配
        for pattern_config in patterns:
            pattern_name = pattern_config.get("name", "")
            pattern = pattern_config.get("pattern", "")
            severity = pattern_config.get("severity", "warning")
            suggest = pattern_config.get("suggest", "")
            problemtype = pattern_config.get("problemtype", "")
            message_template = pattern_config.get("message_template", "发现匹配模式: {pattern}")

            if not pattern:
                continue

            import re
            try:
                # 支持跨行匹配 - 对整个文件内容进行匹配
                matches = re.finditer(pattern, file_content, re.DOTALL)
                for match in matches:
                    # 计算匹配位置的行号
                    match_start = match.start()
                    match_end = match.end()

                    # 计算起始行号
                    start_line = file_content[:match_start].count('\n') + 1
                    end_line = file_content[:match_end].count('\n') + 1

                    # 获取匹配的文本（清理换行符用于显示）
                    matched_text = match.group(0)
                    matched_text_clean = ' '.join(matched_text.split())

                    # 检查是否在注释中（检查匹配开始位置所在的行）
                    start_line_content = lines[start_line - 1] if start_line <= len(lines) else ""
                    match_pos_in_line = match_start - file_content[:match_start].rfind('\n') - 1
                    if match_pos_in_line < 0:
                        match_pos_in_line = match_start

                    if self._is_in_comment(start_line_content, match_pos_in_line):
                        continue

                    # 对于跨行匹配，还需要检查是否整个匹配都在注释中
                    if self._is_multiline_match_in_comment(file_content, match_start, match_end, lines):
                        continue

                    issues.append({
                        "file": file_path,
                        "type": "structure_check",
                        "category": "regex_pattern_match",
                        "severity": severity,
                        "message": message_template.format(
                            pattern=pattern_name,
                            line=start_line,
                            match=matched_text_clean
                        ),
                        "line": start_line,
                        "end_line": end_line,
                        "element": "code_pattern",
                        "element_name": pattern_name,
                        "class_name": class_name,
                        "pattern": pattern,
                        "matched_text": matched_text_clean,
                        "suggest": suggest,
                        "problemtype": problemtype,
                        "rule_type": "regex_pattern_match"
                    })

            except re.error as e:
                print(f"警告: 正则表达式 '{pattern}' 语法错误: {e}")
                continue

        return issues

    def _is_in_comment(self, line: str, position: int) -> bool:
        """
        检查指定位置是否在注释中

        Args:
            line: 代码行
            position: 位置

        Returns:
            是否在注释中
        """
        # 简单检查单行注释
        comment_pos = line.find('//')
        if comment_pos != -1 and position >= comment_pos:
            return True

        # 检查是否在多行注释中
        line_stripped = line.strip()
        if line_stripped.startswith('*') or line_stripped.startswith('/*'):
            return True

        # 检查多行注释开始
        multiline_start = line.find('/*')
        if multiline_start != -1 and position >= multiline_start:
            # 检查是否在同一行有结束
            multiline_end = line.find('*/', multiline_start)
            if multiline_end == -1 or position < multiline_end:
                return True

        return False

    def _is_multiline_match_in_comment(self, file_content: str, match_start: int, match_end: int, lines: List[str]) -> bool:
        """
        检查跨行匹配是否在注释中

        Args:
            file_content: 文件内容
            match_start: 匹配开始位置
            match_end: 匹配结束位置
            lines: 文件行列表

        Returns:
            是否在注释中
        """
        # 简单检查：如果匹配跨越多行，检查每一行是否都在注释中
        start_line = file_content[:match_start].count('\n')
        end_line = file_content[:match_end].count('\n')

        if start_line == end_line:
            # 单行匹配，已经在上面检查过了
            return False

        # 检查是否在多行注释块中
        # 查找匹配开始位置之前最近的多行注释开始
        before_match = file_content[:match_start]
        last_comment_start = before_match.rfind('/*')
        last_comment_end = before_match.rfind('*/')

        # 如果最近的多行注释开始在最近的多行注释结束之后，说明在多行注释中
        if last_comment_start > last_comment_end:
            # 检查匹配结束位置是否在注释结束之前
            after_match_start = file_content[match_start:]
            next_comment_end = after_match_start.find('*/')
            if next_comment_end != -1 and match_end <= match_start + next_comment_end:
                return True

        return False
