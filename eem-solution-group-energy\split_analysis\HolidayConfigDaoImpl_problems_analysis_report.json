{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "problems_count": 4, "analysis_results": [{"class_name": "LambdaQueryWrapper", "line": [3, 34], "description": "未解析的引用 LambdaQueryWrapper", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper", "com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper"], "suggest": "请使用新的类路径替换"}, {"class_name": "ModelDaoImpl", "line": [4, 19], "description": "未解析的引用 ModelDaoImpl", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl"], "suggest": "请使用新的类路径替换"}, {"class_name": "HolidayConfigDao", "line": [5, 19], "description": "未解析的引用 HolidayConfigDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.HolidayConfigDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "HolidayConfig", "line": [6, 19, 28, 34, 34], "description": "未解析的引用 HolidayConfig", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.HolidayConfig"], "suggest": "请使用新的类路径替换"}]}