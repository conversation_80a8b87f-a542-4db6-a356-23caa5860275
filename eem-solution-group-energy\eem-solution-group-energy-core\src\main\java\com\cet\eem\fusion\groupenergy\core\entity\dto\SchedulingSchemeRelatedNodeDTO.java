package com.cet.eem.fusion.groupenergy.core.entity.dto;

import com.cet.eem.common.model.BaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 排班方案关联节点dto
 *
 * <AUTHOR>
 */
@ApiModel(value = "SchedulingSchemeRelatedNodeDTO", description = "排班方案关联假期dto")
@Data
public class SchedulingSchemeRelatedNodeDTO {

    @ApiModelProperty(value = "方案id", required = true)
    private Long schedulingSchemeId;

    @ApiModelProperty(value = "节点", required = true)
    private List<BaseVo> nodeList;
}
