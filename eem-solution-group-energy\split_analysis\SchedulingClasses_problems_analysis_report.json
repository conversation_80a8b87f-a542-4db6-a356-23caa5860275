{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingClasses.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.entity.po", "problems_count": 3, "analysis_results": [{"class_name": "<PERSON><PERSON><PERSON><PERSON>", "line": [3, 18], "description": "未解析的引用 ModelLabel", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.annotation.ModelLabel"], "suggest": "请使用新的类路径替换"}, {"class_name": "BaseEntity", "line": [4, 19], "description": "未解析的引用 BaseEntity", "matched_class_paths": ["com.cet.electric.baseconfig.common.base.BaseEntity"], "suggest": "请使用新的类路径替换"}, {"class_name": "TableNameDef", "line": [5], "description": "未解析的引用 TableNameDef", "matched_class_paths": [], "suggest": "类已经废弃"}]}