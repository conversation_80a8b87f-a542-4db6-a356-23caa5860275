{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service", "problems_count": 5, "analysis_results": [{"class_name": "ClassesEnergyInfoQueryDTO", "line": [3, 49], "description": "未解析的引用 ClassesEnergyInfoQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyInfoQueryDTO", "line": [4, 25, 33, 41], "description": "未解析的引用 TeamGroupEnergyInfoQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO"], "suggest": "请使用新的类路径替换"}, {"class_name": "ClassesEnergyInfoVO", "line": [5, 41, 49], "description": "未解析的引用 ClassesEnergyInfoVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyHistogramVO", "line": [6, 33], "description": "未解析的引用 TeamGroupEnergyHistogramVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyInfoVO", "line": [7, 25], "description": "未解析的引用 TeamGroupEnergyInfoVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO"], "suggest": "请使用新的类路径替换"}]}