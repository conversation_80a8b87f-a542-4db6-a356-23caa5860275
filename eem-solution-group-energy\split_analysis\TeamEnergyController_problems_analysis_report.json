{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.controller", "problems_count": 6, "analysis_results": [{"class_name": "Result", "line": [3, 37, 43, 49, 55, 61], "description": "未解析的引用 Result", "matched_class_paths": ["com.cet.eem.fusion.common.entity.Result", "com.cet.electric.matterhorn.cloud.authservice.common.entity.Result"], "suggest": "请使用新的类路径替换"}, {"class_name": "ClassesEnergyInfoQueryDTO", "line": [5, 55], "description": "未解析的引用 ClassesEnergyInfoQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyInfoQueryDTO", "line": [6, 37, 43, 49], "description": "未解析的引用 TeamGroupEnergyInfoQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO"], "suggest": "请使用新的类路径替换"}, {"class_name": "ClassesEnergyInfoVO", "line": [7, 49, 55], "description": "未解析的引用 ClassesEnergyInfoVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyHistogramVO", "line": [8, 43], "description": "未解析的引用 TeamGroupEnergyHistogramVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyInfoVO", "line": [9, 37], "description": "未解析的引用 TeamGroupEnergyInfoVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO"], "suggest": "请使用新的类路径替换"}]}