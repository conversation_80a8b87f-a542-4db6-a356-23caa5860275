package com.cet.eem.fusion.refrigeration.core;

import com.cet.eem.fusion.refrigeration.core.config.EemFusionRefrigerationBeanNameGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 描述：制冷系统插件自动配置
 * <AUTHOR> (2025-08-11)
 */
@Slf4j
@Configuration
@EnableFeignClients(value = "com.cet.eem.fusion.common.feign.feign")
@ComponentScan(value = {"com.cet.eem.fusion.refrigeration"},
        nameGenerator = EemFusionRefrigerationBeanNameGenerator.class)
public class RefrigerationConfigAutoConfiguration {
    public RefrigerationConfigAutoConfiguration(){
        log.info("Load eem solution 制冷系统插件.");
    }
}
