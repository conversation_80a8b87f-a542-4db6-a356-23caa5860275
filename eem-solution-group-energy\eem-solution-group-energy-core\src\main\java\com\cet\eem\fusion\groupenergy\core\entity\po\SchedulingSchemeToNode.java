package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.model.model.BaseEntity;
import com.cet.piem.common.constant.TableNameDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 排班方案关联节点
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ModelLabel(TableNameDef.SCHEDULING_SCHEME_TO_NODE)
public class SchedulingSchemeToNode extends BaseEntity {

    @ApiModelProperty("排班方案表")
    @JsonProperty("schedulingschemeid")
    private Long schedulingSchemeId;

    @ApiModelProperty("节点类型")
    @JsonProperty("objectlabel")
    private String objectLabel;

    @ApiModelProperty("节点id")
    @JsonProperty("objectid")
    private Long objectId;

    public SchedulingSchemeToNode() {
        this.modelLabel= TableNameDef.SCHEDULING_SCHEME_TO_NODE;
    }
}
