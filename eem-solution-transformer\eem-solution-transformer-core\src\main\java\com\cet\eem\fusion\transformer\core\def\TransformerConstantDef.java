package com.cet.eem.fusion.transformer.core.def;

/**
 * 变压器能效分析插件常量定义
 * <AUTHOR> (2025-01-12)
 */
public interface TransformerConstantDef {

    int INT_ZERO = 0;

    Integer ZERO = 0;

    Integer ONE = 1;

    Integer TWO = 2;

    Integer ONEHUNDRED= 100;

    Long HE_ZHA= 9010002L;

    Long FEN_ZHA= 9010001L;

    Long ONEDAY = 86400000L;

    Double ZEROPOINTSEVENFIVE = 0.75D;

    Double ONEPOINTTHREETHREE = 1.33D;

    Double DOUBLE_ONEHUNDRED= 100D;

    String SYSTEMEVENT = "systemevent";

    String ENERGYTYPE = "energytype";

    String OBJECT_LABEL = "object_label";

    String EVENTTIME = "eventtime";

    String PECEVENTEXTEND = "peceventextend";

    String MONITOREDLABEL = "monitoredlabel";

    String MONITOREDID = "monitoredid";

    String POWERTRANSFORMER = "powertransformer";

    String TRANSFORMERINDEXDATA = "transformerindexdata";

    String LOGTIME = "logtime";

    String TYPE = "type";

    String AGGREGATIONCYCLE = "aggregationcycle";

    String VALUE = "value";

    String COLUMN_AGGREGATION_CYCLE = "aggregationcycle";
}
