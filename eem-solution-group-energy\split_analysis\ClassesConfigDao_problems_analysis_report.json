{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao", "problems_count": 2, "analysis_results": [{"class_name": "BaseModelDao", "line": [3, 11], "description": "未解析的引用 BaseModelDao", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.BaseModelDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "ClassesConfig", "line": [4, 11], "description": "未解析的引用 ClassesConfig", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig"], "suggest": "请使用新的类路径替换"}]}