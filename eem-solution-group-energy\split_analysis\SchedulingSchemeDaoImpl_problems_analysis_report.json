{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "problems_count": 9, "analysis_results": [{"class_name": "ResultWithTotal", "line": [3, 45], "description": "未解析的引用 ResultWithTotal", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "PageUtils", "line": [4], "description": "未解析的引用 PageUtils", "matched_class_paths": ["com.cet.eem.fusion.common.utils.page.PageUtils"], "suggest": "请使用新的类路径替换"}, {"class_name": "ModelDaoImpl", "line": [5, 26], "description": "未解析的引用 ModelDaoImpl", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl"], "suggest": "请使用新的类路径替换"}, {"class_name": "ParentQueryConditionBuilder", "line": [6, 46, 68, 81, 99], "description": "未解析的引用 ParentQueryConditionBuilder", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder"], "suggest": "请使用新的类路径替换"}, {"class_name": "TableColumnNameDef", "line": [7], "description": "未解析的引用 TableColumnNameDef", "matched_class_paths": ["com.cet.eem.solution.common.def.common.label.TableColumnNameDef"], "suggest": "请使用新的类路径替换"}, {"class_name": "TableNameDef", "line": [8], "description": "未解析的引用 TableNameDef", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "SchedulingSchemeDao", "line": [9, 26], "description": "未解析的引用 SchedulingSchemeDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingScheme", "line": [10, 26, 45, 55, 55, 57, 67, 70, 80, 85, 85, 98, 102], "description": "未解析的引用 SchedulingScheme", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingSchemeQueryDTO", "line": [11, 45], "description": "未解析的引用 SchedulingSchemeQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO"], "suggest": "请使用新的类路径替换"}]}