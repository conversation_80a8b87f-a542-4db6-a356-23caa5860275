package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.model.model.BaseEntity;
import com.cet.piem.common.constant.TableNameDef;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 班次方案表
 *
 * <AUTHOR>
 * @date 2024/12/20 14:25
 */
@Data
@ModelLabel(TableNameDef.CLASSES_SCHEME)
public class ClassesScheme extends BaseEntity {

    @ApiModelProperty("班次配置")
    private List<ClassesConfig> classesconfig_model;

    public ClassesScheme() {
        this.modelLabel = TableNameDef.CLASSES_SCHEME;
    }
}