<?xml version='1.0' encoding='cp936'?>
<problems><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>23</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeAddUpdateDTO</description>
  <highlighted_element>SchedulingSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>40</offset>
  <length>28</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>74</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>38</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;SchedulingSchemeDetailVO&gt; allSchedulingScheme()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedHolidayDTO</description>
  <highlighted_element>SchedulingSchemeRelatedHolidayDTO</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>33</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>71</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedNodeDTO</description>
  <highlighted_element>SchedulingSchemeRelatedNodeDTO</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>30</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>79</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;BaseVo&gt; querySchedulingSchemeRelatedNode(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>87</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeAddUpdateDTO</description>
  <highlighted_element>ClassesSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>95</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>111</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoAddUpdateDTO</description>
  <highlighted_element>TeamGroupInfoAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>127</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>135</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesSaveDTO</description>
  <highlighted_element>SchedulingClassesSaveDTO</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>144</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>154</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>161</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;SchedulingSchemeDetailVO&gt; queryProduceSchedulingSchemeByType(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>24</length>
</problem>
</problems>