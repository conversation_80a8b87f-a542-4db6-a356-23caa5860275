package com.cet.eem.fusion.groupenergy.core.dao.impl;

import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.piem.common.def.AggregationCycle;
import com.cet.piem.dao.classes.TeamGroupEnergyDao;
import com.cet.piem.entity.classes.TeamGroupEnergy;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 班组能耗dao
 *
 * <AUTHOR>
 */
@Component
public class TeamGroupEnergyDaoImpl extends ModelDaoImpl<TeamGroupEnergy> implements TeamGroupEnergyDao {

    /**
     * 查询班组能耗数据
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param energyType   能源类型
     * @param nodeId       节点id
     * @param nodeLabel    节点类型
     * @param teamGroupIds 班组id集合
     * @return 基础能耗数据
     */
    @Override
    public List<TeamGroupEnergy> queryTeamGroupEnergy(Long startTime, Long endTime, Integer energyType, Long nodeId, String nodeLabel, List<Long> teamGroupIds, Integer cycle) {

        if (CollectionUtils.isEmpty(teamGroupIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TeamGroupEnergy> wrapper = LambdaQueryWrapper.of(TeamGroupEnergy.class)
                .in(TeamGroupEnergy::getTeamGroupId, teamGroupIds)
                .ge(TeamGroupEnergy::getLogTime, startTime)
                .lt(TeamGroupEnergy::getLogTime, endTime)
                .eq(TeamGroupEnergy::getEnergyType, energyType)
                .eq(TeamGroupEnergy::getObjectId, nodeId)
                .eq(TeamGroupEnergy::getObjectLabel, nodeLabel)
                .eq(TeamGroupEnergy::getAggregationCycle, cycle)
                // 筛选掉聚合的数据
                .ne(TeamGroupEnergy::getClassesId, -1);
        return selectList(wrapper);
    }

    /**
     * 查询班次基础能耗数据
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param energyType       能源类型
     * @param nodeId           节点id
     * @param nodeLabel        节点类型
     * @param classesConfigIds 班次id集合
     * @return 基础能耗数据
     */
    @Override
    public List<TeamGroupEnergy> queryClassesConfigDayEnergy(Long startTime, Long endTime, Integer energyType, Long nodeId, String nodeLabel, List<Long> classesConfigIds) {
        if (CollectionUtils.isEmpty(classesConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TeamGroupEnergy> wrapper = LambdaQueryWrapper.of(TeamGroupEnergy.class)
                .in(TeamGroupEnergy::getClassesId, classesConfigIds)
                .ge(TeamGroupEnergy::getLogTime, startTime)
                .lt(TeamGroupEnergy::getLogTime, endTime)
                .eq(TeamGroupEnergy::getEnergyType, energyType)
                .eq(TeamGroupEnergy::getObjectId, nodeId)
                .eq(TeamGroupEnergy::getObjectLabel, nodeLabel)
                .eq(TeamGroupEnergy::getAggregationCycle, AggregationCycle.ONE_DAY);


        return selectList(wrapper);
    }

    /**
     * 查询班次基础能耗数据
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param energyType       能源类型
     * @param nodeId           节点id
     * @param nodeLabel        节点类型
     * @param classesConfigIds 班次id集合
     * @param teamGroupIdList  班组id集合
     * @return 基础能耗数据
     */
    @Override
    public List<TeamGroupEnergy> queryClassesConfigDayEnergy(Long startTime, Long endTime, Integer energyType, Long nodeId, String nodeLabel,
                                                             List<Long> classesConfigIds, List<Long> teamGroupIdList) {
        if (CollectionUtils.isEmpty(teamGroupIdList) || CollectionUtils.isEmpty(classesConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TeamGroupEnergy> wrapper = LambdaQueryWrapper.of(TeamGroupEnergy.class)
                .in(TeamGroupEnergy::getClassesId, classesConfigIds)
                .in(TeamGroupEnergy::getTeamGroupId, teamGroupIdList)
                .ge(TeamGroupEnergy::getLogTime, startTime)
                .lt(TeamGroupEnergy::getLogTime, endTime)
                .eq(TeamGroupEnergy::getEnergyType, energyType)
                .eq(TeamGroupEnergy::getObjectId, nodeId)
                .eq(TeamGroupEnergy::getObjectLabel, nodeLabel)
                .eq(TeamGroupEnergy::getAggregationCycle, AggregationCycle.ONE_DAY);

        return selectList(wrapper);
    }
}
