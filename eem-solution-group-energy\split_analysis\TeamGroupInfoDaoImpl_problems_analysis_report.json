{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "problems_count": 3, "analysis_results": [{"class_name": "ModelDaoImpl", "line": [3, 14], "description": "未解析的引用 ModelDaoImpl", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupInfoDao", "line": [4, 14], "description": "未解析的引用 TeamGroupInfoDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.TeamGroupInfoDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupInfo", "line": [5, 14], "description": "未解析的引用 TeamGroupInfo", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo"], "suggest": "请使用新的类路径替换"}]}