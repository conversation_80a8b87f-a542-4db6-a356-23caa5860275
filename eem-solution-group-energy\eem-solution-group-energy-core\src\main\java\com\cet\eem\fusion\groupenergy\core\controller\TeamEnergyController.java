package com.cet.eem.fusion.groupenergy.core.controller;

import com.cet.eem.common.model.Result;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import com.cet.piem.entity.dto.classes.ClassesEnergyInfoQueryDTO;
import com.cet.piem.entity.dto.classes.TeamGroupEnergyInfoQueryDTO;
import com.cet.piem.entity.vo.classes.ClassesEnergyInfoVO;
import com.cet.piem.entity.vo.classes.TeamGroupEnergyHistogramVO;
import com.cet.piem.entity.vo.classes.TeamGroupEnergyInfoVO;
import com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 班组能耗相关接口
 *
 * <AUTHOR>
 */
@Validated
@RestController
@Api(value = PluginInfoDef.GroupEnergy.INTERFACE_PREFIX + "/v1/energy", tags = "班组能耗相关接口")
@RequestMapping(value = PluginInfoDef.GroupEnergy.PLUGIN_NAME_PREFIX + "/v1/energy")
public class TeamEnergyController {

    @Resource
    private TeamEnergyService teamEnergyService;

    @PostMapping("/teamGroupEnergy/query")
    @ApiOperation("查询班组能耗数据")
    public Result<TeamGroupEnergyInfoVO> queryTeamGroupEnergyInfo(@RequestBody TeamGroupEnergyInfoQueryDTO dto) {
        return Result.ok(teamEnergyService.queryTeamGroupEnergyInfo(dto));
    }

    @PostMapping("/teamGroupEnergy/histogram/query")
    @ApiOperation("班组用能柱状图查询")
    public Result<List<TeamGroupEnergyHistogramVO>> queryTeamGroupEnergyHistogram(@RequestBody TeamGroupEnergyInfoQueryDTO dto) {
        return Result.ok(teamEnergyService.queryTeamGroupEnergyHistogram(dto));
    }

    @PostMapping("/classesEnergy/compare/query")
    @ApiOperation("查询班次对比能耗数据")
    public Result<List<ClassesEnergyInfoVO>> queryClassesEnergyCompare(@RequestBody TeamGroupEnergyInfoQueryDTO dto) {
        return Result.ok(teamEnergyService.queryClassesEnergyCompare(dto));
    }

    @PostMapping("/classesEnergy/query")
    @ApiOperation("根据条件查询班次能耗")
    public Result<ClassesEnergyInfoVO> queryClassesEnergy(@RequestBody ClassesEnergyInfoQueryDTO dto) {
        return Result.ok(teamEnergyService.queryClassesEnergy(dto));
    }

    @PostMapping("/classesEnergy/projectTree")
    @ApiOperation("班次能耗节点树查询")
    public Result<List<Map<String, Object>>> classesEnergyProjectTree(@RequestParam @ApiParam(name = "energyType", value = "能源类型", required = true)
                                                                                  Integer energyType,
                                                                      @RequestParam @ApiParam(name = "schedulingSchemeId", value = "排班方案id", required = true)
                                                                              Long schedulingSchemeId) {
        return Result.ok(teamEnergyService.classesEnergyProjectTree(energyType, schedulingSchemeId));
    }
}
