<?xml version='1.0' encoding='cp936'?>
<problems><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 bll</description>
  <highlighted_element>bll</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 domain</description>
  <highlighted_element>domain</highlighted_element>
  <language>JAVA</language>
  <offset>36</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 object</description>
  <highlighted_element>object</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 physicalquantity</description>
  <highlighted_element>physicalquantity</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ProjectUnitClassify</description>
  <highlighted_element>ProjectUnitClassify</highlighted_element>
  <language>JAVA</language>
  <offset>67</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 bll</description>
  <highlighted_element>bll</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 domain</description>
  <highlighted_element>domain</highlighted_element>
  <language>JAVA</language>
  <offset>36</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 object</description>
  <highlighted_element>object</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 physicalquantity</description>
  <highlighted_element>physicalquantity</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserDefineUnit</description>
  <highlighted_element>UserDefineUnit</highlighted_element>
  <language>JAVA</language>
  <offset>67</offset>
  <length>14</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 CommonUtils</description>
  <highlighted_element>CommonUtils</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 constant</description>
  <highlighted_element>constant</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>8</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 EnumOperationType</description>
  <highlighted_element>EnumOperationType</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 definition</description>
  <highlighted_element>definition</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>10</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ColumnDef</description>
  <highlighted_element>ColumnDef</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>9</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 utils</description>
  <highlighted_element>utils</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TimeUtil</description>
  <highlighted_element>TimeUtil</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>8</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 constant</description>
  <highlighted_element>constant</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>8</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TableColumnNameDef</description>
  <highlighted_element>TableColumnNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>36</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 def</description>
  <highlighted_element>def</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 AggregationCycle</description>
  <highlighted_element>AggregationCycle</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 utils</description>
  <highlighted_element>utils</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 DoubleUtils</description>
  <highlighted_element>DoubleUtils</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyDao</description>
  <highlighted_element>TeamGroupEnergyDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>16</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>16</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>16</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyCard</description>
  <highlighted_element>TeamGroupEnergyCard</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>23</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>23</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>23</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UnitService</description>
  <highlighted_element>UnitService</highlighted_element>
  <language>JAVA</language>
  <offset>28</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 impl</description>
  <highlighted_element>impl</highlighted_element>
  <language>JAVA</language>
  <offset>28</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 NodeServiceImpl</description>
  <highlighted_element>NodeServiceImpl</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 teamenergy</description>
  <highlighted_element>teamenergy</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>10</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamEnergyService</description>
  <highlighted_element>TeamEnergyService</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>41</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamEnergyService</description>
  <highlighted_element>TeamEnergyService</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>44</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl schedulingSchemeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>47</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl teamGroupEnergyDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyDao</description>
  <highlighted_element>TeamGroupEnergyDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>50</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl unitService" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UnitService</description>
  <highlighted_element>UnitService</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>53</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl nodeService" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 NodeServiceImpl</description>
  <highlighted_element>NodeServiceImpl</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>56</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl schemeToNodeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>65</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>65</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>67</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>67</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>70</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>76</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>80</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>88</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserDefineUnit</description>
  <highlighted_element>UserDefineUnit</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>14</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>98</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>101</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyCard</description>
  <highlighted_element>TeamGroupEnergyCard</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>102</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>103</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyCard</description>
  <highlighted_element>TeamGroupEnergyCard</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>103</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyCard</description>
  <highlighted_element>TeamGroupEnergyCard</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>106</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>108</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>153</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>153</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>74</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>161</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>167</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>170</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>178</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>186</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserDefineUnit</description>
  <highlighted_element>UserDefineUnit</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>14</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>190</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>193</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>193</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>199</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>204</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>204</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>205</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>208</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>211</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>216</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>217</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>217</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>217</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>93</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>217</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>120</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>219</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>232</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>232</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>232</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>232</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>116</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>257</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.lang.Double unitConversion(java.lang.Double value, UserDefineUnit unit)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserDefineUnit</description>
  <highlighted_element>UserDefineUnit</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>14</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>271</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>271</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>63</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>272</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>275</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>281</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>282</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>294</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>302</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserDefineUnit</description>
  <highlighted_element>UserDefineUnit</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>14</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>305</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>310</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>310</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>41</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>316</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>62</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>82</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>320</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>372</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>372</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>373</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>373</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>376</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>382</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>383</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>396</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>404</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserDefineUnit</description>
  <highlighted_element>UserDefineUnit</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>14</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>407</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>427</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>436</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>436</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>437</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>441</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>446</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>446</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>446</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>62</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>446</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>82</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>468</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>469</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>469</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>67</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>481</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; filterAssociationNode(java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; nodeList, java.util.List&lt;BaseVo&gt; toNodes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>101</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>497</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; filterAssociationNode(java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; nodeList, java.util.List&lt;BaseVo&gt; toNodes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>497</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; filterAssociationNode(java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; nodeList, java.util.List&lt;BaseVo&gt; toNodes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>6</length>
</problem>
</problems>