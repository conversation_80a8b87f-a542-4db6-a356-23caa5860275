"""
Java代码解析服务
使用javalang库进行准确的Java代码解析
"""

import logging
from typing import Optional, List, Dict, Any
import javalang
import os

logger = logging.getLogger(__name__)


class JavaParserService:
    """Java代码解析服务"""
    
    def __init__(self):
        """初始化Java解析服务"""
        pass
    
    def extract_main_class_name(self, file_path: str, code_content: str) -> str:
        """
        提取Java文件的主类名
        
        Args:
            file_path (str): Java文件路径
            code_content (str): Java代码内容
            
        Returns:
            str: 主类名
        """
        try:
            # 首先尝试使用javalang解析
            parsed_class_name = self._parse_with_javalang(code_content)
            if parsed_class_name:
                return parsed_class_name
            
            # 如果javalang解析失败，使用文件名作为备选
            file_name = os.path.basename(file_path)
            if file_name.endswith('.java'):
                return file_name[:-5]  # 去掉.java扩展名
            
            return file_name
            
        except Exception as e:
            logger.warning(f"解析Java文件失败 {file_path}: {e}")
            # 出错时使用文件名
            file_name = os.path.basename(file_path)
            return file_name[:-5] if file_name.endswith('.java') else file_name
    
    def _parse_with_javalang(self, code_content: str) -> Optional[str]:
        """
        使用javalang解析Java代码，提取主类名
        
        Args:
            code_content (str): Java代码内容
            
        Returns:
            Optional[str]: 主类名，如果解析失败返回None
        """
        try:
            # 使用javalang解析Java代码
            tree = javalang.parse.parse(code_content)
            
            # 查找所有类型声明（类、枚举、接口）
            type_declarations = []

            # 直接遍历编译单元的类型声明
            if hasattr(tree, 'types') and tree.types:
                for type_decl in tree.types:
                    if isinstance(type_decl, (javalang.tree.ClassDeclaration,
                                           javalang.tree.EnumDeclaration,
                                           javalang.tree.InterfaceDeclaration)):
                        type_declarations.append({
                            'name': type_decl.name,
                            'type': type(type_decl).__name__,
                            'modifiers': type_decl.modifiers if hasattr(type_decl, 'modifiers') else [],
                            'is_public': 'public' in (type_decl.modifiers if hasattr(type_decl, 'modifiers') else [])
                        })
            
            if not type_declarations:
                return None
            
            # 优先选择public的类型声明
            public_declarations = [d for d in type_declarations if d['is_public']]
            if public_declarations:
                # 如果有多个public声明，选择第一个
                return public_declarations[0]['name']
            
            # 如果没有public声明，选择第一个类型声明
            return type_declarations[0]['name']
            
        except javalang.parser.JavaSyntaxError as e:
            logger.debug(f"Java语法解析错误: {e}")
            return None
        except Exception as e:
            logger.debug(f"javalang解析异常: {e}")
            return None
    

    
    def extract_package_name(self, code_content: str) -> str:
        """
        提取Java文件的包名
        
        Args:
            code_content (str): Java代码内容
            
        Returns:
            str: 包名，如果没有包声明返回"未知包名"
        """
        try:
            # 使用javalang解析
            tree = javalang.parse.parse(code_content)
            
            if tree.package and tree.package.name:
                # 包名是一个点分隔的标识符字符串
                return tree.package.name
            
            return "未知包名"
            
        except Exception as e:
            logger.debug(f"包名解析失败: {e}")
            return "未知包名"
    
    def get_class_info(self, code_content: str) -> Dict[str, Any]:
        """
        获取Java文件的详细类信息
        
        Args:
            code_content (str): Java代码内容
            
        Returns:
            Dict[str, Any]: 类信息字典
        """
        try:
            tree = javalang.parse.parse(code_content)
            
            info = {
                'package_name': self.extract_package_name(code_content),
                'imports': [],
                'classes': [],
                'enums': [],
                'interfaces': []
            }
            
            # 提取导入信息
            if tree.imports:
                for imp in tree.imports:
                    info['imports'].append({
                        'path': imp.path,
                        'static': imp.static,
                        'wildcard': imp.wildcard
                    })
            
            # 提取类型声明信息
            if hasattr(tree, 'types') and tree.types:
                for type_decl in tree.types:
                    if isinstance(type_decl, javalang.tree.ClassDeclaration):
                        info['classes'].append({
                            'name': type_decl.name,
                            'modifiers': type_decl.modifiers if hasattr(type_decl, 'modifiers') else [],
                            'extends': type_decl.extends.name if type_decl.extends else None,
                            'implements': [impl.name for impl in type_decl.implements] if type_decl.implements else []
                        })
                    elif isinstance(type_decl, javalang.tree.EnumDeclaration):
                        info['enums'].append({
                            'name': type_decl.name,
                            'modifiers': type_decl.modifiers if hasattr(type_decl, 'modifiers') else [],
                            'implements': [impl.name for impl in type_decl.implements] if type_decl.implements else []
                        })
                    elif isinstance(type_decl, javalang.tree.InterfaceDeclaration):
                        info['interfaces'].append({
                            'name': type_decl.name,
                            'modifiers': type_decl.modifiers if hasattr(type_decl, 'modifiers') else [],
                            'extends': [ext.name for ext in type_decl.extends] if type_decl.extends else []
                        })
            
            return info
            
        except Exception as e:
            logger.warning(f"获取类信息失败: {e}")
            return {
                'package_name': "未知包名",
                'imports': [],
                'classes': [],
                'enums': [],
                'interfaces': []
            }
    
    def validate_java_syntax(self, code_content: str) -> bool:
        """
        验证Java代码语法是否正确
        
        Args:
            code_content (str): Java代码内容
            
        Returns:
            bool: 语法是否正确
        """
        try:
            javalang.parse.parse(code_content)
            return True
        except javalang.parser.JavaSyntaxError:
            return False
        except Exception:
            return False
