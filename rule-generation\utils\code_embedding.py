"""
代码向量化工具类
使用CodeBERT模型将代码转换为向量表示
"""

import torch
from transformers import AutoTokenizer, AutoModel
from typing import List
import logging
from config.settings import MODEL_CONFIG

logger = logging.getLogger(__name__)


class CodeEmbedding:
    """代码向量化工具类"""
    
    def __init__(self):
        """初始化CodeBERT模型和分词器"""
        try:
            logger.info("正在加载CodeBERT模型...")
            self.tokenizer = AutoTokenizer.from_pretrained(MODEL_CONFIG["model_name"])
            self.model = AutoModel.from_pretrained(MODEL_CONFIG["model_name"])
            self.max_length = MODEL_CONFIG["max_length"]
            logger.info("CodeBERT模型加载成功")
        except Exception as e:
            logger.error(f"加载CodeBERT模型失败: {e}")
            raise
    
    def get_code_embedding(self, code: str) -> torch.Tensor:
        """
        获取单个代码片段的向量表示
        Args:
            code (str): 代码字符串
        Returns:
            torch.Tensor: 代码的向量表示 (768维)
        """
        try:
            # 分词处理
            inputs = self.tokenizer(
                code, 
                return_tensors="pt", 
                padding=True, 
                truncation=True, 
                max_length=self.max_length
            )
            
            # 生成嵌入向量
            with torch.no_grad():
                outputs = self.model(**inputs)
            
            # 提取[CLS] token的向量作为整体表示
            embedding = outputs.last_hidden_state[:, 0, :].squeeze()
            return embedding
            
        except Exception as e:
            logger.error(f"生成代码向量失败: {e}")
            raise
    
    def batch_get_embeddings(self, codes: List[str]) -> List[torch.Tensor]:
        """
        批量获取代码向量
        Args:
            codes (List[str]): 代码字符串列表
        Returns:
            List[torch.Tensor]: 向量列表
        """
        embeddings = []
        for i, code in enumerate(codes):
            try:
                embedding = self.get_code_embedding(code)
                embeddings.append(embedding)
                if (i + 1) % 10 == 0:
                    logger.info(f"已处理 {i + 1}/{len(codes)} 个代码片段")
            except Exception as e:
                logger.warning(f"处理第 {i + 1} 个代码片段失败: {e}")
                # 添加零向量作为占位符
                embeddings.append(torch.zeros(768))
        
        return embeddings
    
    def embedding_to_list(self, embedding: torch.Tensor) -> List[float]:
        """
        将Tensor向量转换为Python列表（用于Milvus存储）
        Args:
            embedding (torch.Tensor): 向量
        Returns:
            List[float]: 向量列表
        """
        return embedding.float().tolist()