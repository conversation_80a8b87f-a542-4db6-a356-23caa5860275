package com.cet.eem.fusion.transformer.core.dao;

import com.cet.eem.fusion.transformer.core.entity.dto.PowerTransformerDto;

import java.util.List;

public interface PowerTransformerDao {

    List<PowerTransformerDto>  queryAll();


    /**
     * 查询变压器信息及变压器所属项目
     * @return
     */
    List<PowerTransformerDto> queryPowerTransformerAndProject();

    /**
     * 根据项目id查询变压器
     * @param projectId
     * @return
     */
    List<PowerTransformerDto>  queryByProjectId(Long projectId);

}
