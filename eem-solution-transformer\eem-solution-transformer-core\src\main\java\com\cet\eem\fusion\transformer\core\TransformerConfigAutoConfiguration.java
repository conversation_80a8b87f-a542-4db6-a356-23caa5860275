package com.cet.eem.fusion.transformer.core;

import com.cet.eem.fusion.transformer.core.config.EemFusionTransformerBeanNameGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 描述：自动配置
 * <AUTHOR> (2025-01-12)
 */
@Slf4j
@Configuration
@EnableFeignClients(value = "com.cet.eem.fusion.common.feign.feign")
@ComponentScan(value = {"com.cet.eem.fusion.transformer"},
        nameGenerator = EemFusionTransformerBeanNameGenerator.class)
public class TransformerConfigAutoConfiguration {
    public TransformerConfigAutoConfiguration(){
        log.info("Load eem solution 变压器能效分析.");
    }
}
