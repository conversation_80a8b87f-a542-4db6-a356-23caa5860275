{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "problems_count": 6, "analysis_results": [{"class_name": "ModelDaoImpl", "line": [3, 21], "description": "未解析的引用 ModelDaoImpl", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl"], "suggest": "请使用新的类路径替换"}, {"class_name": "ParentQueryConditionBuilder", "line": [4, 35], "description": "未解析的引用 ParentQueryConditionBuilder", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder"], "suggest": "请使用新的类路径替换"}, {"class_name": "TableColumnNameDef", "line": [5], "description": "未解析的引用 TableColumnNameDef", "matched_class_paths": ["com.cet.eem.solution.common.def.common.label.TableColumnNameDef"], "suggest": "请使用新的类路径替换"}, {"class_name": "TableNameDef", "line": [6], "description": "未解析的引用 TableNameDef", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "ClassesSchemeDao", "line": [7, 21], "description": "未解析的引用 ClassesSchemeDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.ClassesSchemeDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "ClassesScheme", "line": [8, 21, 30, 39, 39], "description": "未解析的引用 ClassesScheme", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme"], "suggest": "请使用新的类路径替换"}]}