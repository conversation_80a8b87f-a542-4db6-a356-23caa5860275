package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.model.model.BaseEntity;
import com.cet.piem.common.constant.TableNameDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 班组能耗表
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ModelLabel(TableNameDef.TEAM_GROUP_ENERGY)
public class TeamGroupEnergy extends BaseEntity {

    @ApiModelProperty("节点id")
    @JsonProperty("objectid")
    private Long objectId;

    @ApiModelProperty("节点类型")
    @JsonProperty("objectlabel")
    private String objectLabel;

    @ApiModelProperty("能源类型")
    @JsonProperty("energytype")
    private Integer energyType;

    @ApiModelProperty("排班日期")
    @JsonProperty("logtime")
    private Long logTime;

    @ApiModelProperty("聚合周期")
    @JsonProperty("aggregationcycle")
    private Integer aggregationCycle;

    @ApiModelProperty("班次id")
    @JsonProperty("classesid")
    private Long classesId;

    @ApiModelProperty("班组id")
    @JsonProperty("teamgroupid")
    private Long teamGroupId;

    @ApiModelProperty("能耗值")
    private Double value;

    public TeamGroupEnergy() {
        this.modelLabel = TableNameDef.TEAM_GROUP_ENERGY;
    }
}
