<?xml version='1.0' encoding='cp936'?>
<problems><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 page</description>
  <highlighted_element>page</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 PageUtils</description>
  <highlighted_element>PageUtils</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>9</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ModelDaoImpl</description>
  <highlighted_element>ModelDaoImpl</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>12</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 tool</description>
  <highlighted_element>tool</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ParentQueryConditionBuilder</description>
  <highlighted_element>ParentQueryConditionBuilder</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 constant</description>
  <highlighted_element>constant</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>8</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TableColumnNameDef</description>
  <highlighted_element>TableColumnNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>36</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 constant</description>
  <highlighted_element>constant</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>8</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TableNameDef</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>36</offset>
  <length>12</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>26</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ModelDaoImpl</description>
  <highlighted_element>ModelDaoImpl</highlighted_element>
  <language>JAVA</language>
  <offset>45</offset>
  <length>12</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>26</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>26</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>87</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>45</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>45</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>45</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>61</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>46</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ParentQueryConditionBuilder</description>
  <highlighted_element>ParentQueryConditionBuilder</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>93</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>57</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>67</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryAll()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>68</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryAll()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ParentQueryConditionBuilder</description>
  <highlighted_element>ParentQueryConditionBuilder</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>70</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryAll()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>80</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>81</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ParentQueryConditionBuilder</description>
  <highlighted_element>ParentQueryConditionBuilder</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>85</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>85</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>93</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>98</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryProduceSchedulingScheme(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>99</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryProduceSchedulingScheme(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ParentQueryConditionBuilder</description>
  <highlighted_element>ParentQueryConditionBuilder</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>102</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryProduceSchedulingScheme(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>16</length>
</problem>
</problems>