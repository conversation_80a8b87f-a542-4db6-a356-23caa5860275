{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "problems_count": 31, "analysis_results": [{"class_name": "ParamUtils", "line": [3], "description": "未解析的引用 ParamUtils", "matched_class_paths": ["com.cet.eem.fusion.common.utils.ParamUtils"], "suggest": "请使用新的类路径替换"}, {"class_name": "BusinessBaseException", "line": [4, 80, 144, 162, 301, 306, 319, 348, 364, 433, 454, 469, 479, 506], "description": "未解析的引用 BusinessBaseException", "matched_class_paths": ["com.cet.eem.fusion.common.exception.BusinessBaseException"], "suggest": "请使用新的类路径替换"}, {"class_name": "BaseVo", "line": [5, 253, 255, 276, 281], "description": "未解析的引用 BaseVo", "matched_class_paths": ["com.cet.eem.fusion.common.model.BaseVo"], "suggest": "请使用新的类路径替换"}, {"class_name": "Result", "line": [6, 535], "description": "未解析的引用 Result", "matched_class_paths": ["com.cet.eem.fusion.common.entity.Result", "com.cet.electric.matterhorn.cloud.authservice.common.entity.Result"], "suggest": "请使用新的类路径替换"}, {"class_name": "ResultWithTotal", "line": [7, 95, 97, 99, 99], "description": "未解析的引用 ResultWithTotal", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "UserVo", "line": [8, 535, 538, 542], "description": "未解析的引用 UserVo", "matched_class_paths": ["com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo"], "suggest": "请使用新的类路径替换"}, {"class_name": "JsonTransferUtils", "line": [9], "description": "未解析的引用 JsonTransferUtils", "matched_class_paths": ["com.cet.eem.fusion.common.utils.JsonTransferUtils", "com.cet.electric.baseconfig.sdk.common.utils.JsonTransferUtils", "com.cet.electric.modelservice.sdk.toolkit.JsonTransferUtils"], "suggest": "请使用新的类路径替换"}, {"class_name": "TimeUtil", "line": [10], "description": "未解析的引用 TimeUtil", "matched_class_paths": ["com.cet.eem.fusion.common.utils.time.TimeUtil", "com.cet.electric.baseconfig.sdk.common.utils.TimeUtil", "com.cet.electric.fusion.matrix.v2.utils.TimeUtil"], "suggest": "请使用新的类路径替换"}, {"class_name": "EemCloudAuthService", "line": [11], "description": "未解析的引用 EemCloudAuthService", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "TeamConfigService", "line": [16, 34], "description": "未解析的引用 TeamConfigService", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.service.TeamConfigService"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingSchemeAddUpdateDTO", "line": [68], "description": "未解析的引用 SchedulingSchemeAddUpdateDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeAddUpdateDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingScheme", "line": [70, 83, 83, 97, 114, 130, 309, 396, 446, 520, 750, 765, 767], "description": "未解析的引用 SchedulingScheme", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeDetailVO", "line": [95, 98, 99, 113, 749, 765, 766, 768, 768], "description": "未解析的引用 SchedulingSchemeDetailVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.SchedulingSchemeDetailVO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeQueryDTO", "line": [95], "description": "未解析的引用 SchedulingSchemeQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingClasses", "line": [142, 160, 362, 431, 467, 504, 573, 579, 581, 581, 608, 619, 626, 631, 679, 694, 701, 706], "description": "未解析的引用 SchedulingClasses", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingClasses"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesScheme", "line": [151, 315, 334, 334, 344, 352, 405, 420, 777], "description": "未解析的引用 ClassesScheme", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeToNode", "line": [168, 243, 254, 256, 256, 277], "description": "未解析的引用 SchedulingSchemeToNode", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeRelatedHolidayDTO", "line": [195], "description": "未解析的引用 SchedulingSchemeRelatedHolidayDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeRelatedHolidayDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "HolidayConfig", "line": [198, 207, 209, 209, 226], "description": "未解析的引用 HolidayConfig", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.HolidayConfig"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeRelatedNodeDTO", "line": [241], "description": "未解析的引用 SchedulingSchemeRelatedNodeDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeRelatedNodeDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesSchemeAddUpdateDTO", "line": [293], "description": "未解析的引用 ClassesSchemeAddUpdateDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesSchemeAddUpdateDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesConfigDTO", "line": [294, 296, 297, 324, 371], "description": "未解析的引用 ClassesConfigDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesConfigDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesConfig", "line": [323, 325, 325, 358, 370, 372, 372, 623, 644, 698, 719], "description": "未解析的引用 ClassesConfig", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesSchemeVO", "line": [394, 404, 406, 406, 776, 778, 778], "description": "未解析的引用 ClassesSchemeVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesSchemeVO"], "suggest": "请添加新的import类导入"}, {"class_name": "TeamGroupInfoAddUpdateDTO", "line": [442], "description": "未解析的引用 TeamGroupInfoAddUpdateDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupInfoAddUpdateDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "TeamGroupInfo", "line": [451, 458, 458, 475, 483, 483, 525, 622, 634, 697, 709, 786], "description": "未解析的引用 TeamGroupInfo", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo"], "suggest": "请添加新的import类导入"}, {"class_name": "TeamGroupInfoVO", "line": [518, 519, 526, 526, 785, 787, 787], "description": "未解析的引用 TeamGroupInfoVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupInfoVO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingClassesSaveDTO", "line": [571], "description": "未解析的引用 SchedulingClassesSaveDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingClassesSaveDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingClassesConfigDTO", "line": [580], "description": "未解析的引用 SchedulingClassesConfigDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingClassesConfigDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingClassesVO", "line": [602, 625, 627, 627, 672, 700, 702, 702], "description": "未解析的引用 SchedulingClassesVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.SchedulingClassesVO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingClassesConfigVO", "line": [630, 632, 632, 705, 707, 707], "description": "未解析的引用 SchedulingClassesConfigVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.SchedulingClassesConfigVO"], "suggest": "请添加新的import类导入"}]}