{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service", "problems_count": 13, "analysis_results": [{"class_name": "BaseVo", "line": [4, 79], "description": "未解析的引用 BaseVo", "matched_class_paths": ["com.cet.eem.fusion.common.model.BaseVo"], "suggest": "请使用新的类路径替换"}, {"class_name": "ResultWithTotal", "line": [5, 31], "description": "未解析的引用 ResultWithTotal", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "SchedulingSchemeAddUpdateDTO", "line": [23], "description": "未解析的引用 SchedulingSchemeAddUpdateDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeAddUpdateDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeDetailVO", "line": [31, 38, 161], "description": "未解析的引用 SchedulingSchemeDetailVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.SchedulingSchemeDetailVO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeQueryDTO", "line": [31], "description": "未解析的引用 SchedulingSchemeQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeRelatedHolidayDTO", "line": [55], "description": "未解析的引用 SchedulingSchemeRelatedHolidayDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeRelatedHolidayDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeRelatedNodeDTO", "line": [71], "description": "未解析的引用 SchedulingSchemeRelatedNodeDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeRelatedNodeDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesSchemeAddUpdateDTO", "line": [87], "description": "未解析的引用 ClassesSchemeAddUpdateDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesSchemeAddUpdateDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesSchemeVO", "line": [95], "description": "未解析的引用 ClassesSchemeVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesSchemeVO"], "suggest": "请添加新的import类导入"}, {"class_name": "TeamGroupInfoAddUpdateDTO", "line": [111], "description": "未解析的引用 TeamGroupInfoAddUpdateDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupInfoAddUpdateDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "TeamGroupInfoVO", "line": [127], "description": "未解析的引用 TeamGroupInfoVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupInfoVO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingClassesSaveDTO", "line": [135], "description": "未解析的引用 SchedulingClassesSaveDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingClassesSaveDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingClassesVO", "line": [144, 154], "description": "未解析的引用 SchedulingClassesVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.SchedulingClassesVO"], "suggest": "请添加新的import类导入"}]}