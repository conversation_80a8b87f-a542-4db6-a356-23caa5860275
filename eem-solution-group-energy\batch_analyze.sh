#!/bin/bash

echo "=========================================="
echo "Java导入问题批量分析工具"
echo "=========================================="
echo

# 设置Maven路径（请根据实际情况修改）
MAVEN_PATH="mvn"

# 设置项目路径
PROJECT_PATH="eem-solution-group-energy-core"

echo "配置信息:"
echo "Maven路径: $MAVEN_PATH"
echo "项目路径: $PROJECT_PATH"
echo

# 检查split_problems目录是否存在
if [ ! -d "split_problems" ]; then
    echo "错误: split_problems目录不存在"
    echo "请先运行problem_splitter.py分割问题文件"
    exit 1
fi

echo "开始批量分析split_problems目录下的问题文件..."
echo

# 统计文件数量
count=$(find split_problems -name "*_problems.xml" -type f | wc -l)

if [ $count -eq 0 ]; then
    echo "警告: split_problems目录下没有找到问题文件"
    exit 1
fi

echo "找到 $count 个问题文件，开始分析..."
echo

# 分析每个问题文件
processed=0
for file in split_problems/*_problems.xml; do
    if [ -f "$file" ]; then
        processed=$((processed + 1))
        echo "[$processed/$count] 正在分析: $(basename "$file")"
        python3 precise_class_finder.py "$file" "$PROJECT_PATH" "$MAVEN_PATH"
        echo
        echo "----------------------------------------"
        echo
    fi
done

echo "=========================================="
echo "批量分析完成！"
echo "处理了 $processed 个文件"
echo "分析报告已保存到 split_analysis 目录"
echo "=========================================="