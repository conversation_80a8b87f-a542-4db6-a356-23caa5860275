package com.cet.eem.fusion.groupenergy.core.dao.impl;

import com.cet.eem.common.model.ResultWithTotal;
import com.cet.eem.common.page.PageUtils;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.tool.ParentQueryConditionBuilder;
import com.cet.piem.common.constant.TableColumnNameDef;
import com.cet.piem.common.constant.TableNameDef;
import com.cet.piem.dao.classes.SchedulingSchemeDao;
import com.cet.piem.entity.classes.SchedulingScheme;
import com.cet.piem.entity.dto.classes.SchedulingSchemeQueryDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 排班方案
 *
 * <AUTHOR>
 */
@Component
public class SchedulingSchemeDaoImpl extends ModelDaoImpl<SchedulingScheme> implements SchedulingSchemeDao {


    /**
     * 排班方案下子节点
     * 1. 班次方案
     * 2. 班次配置
     * 3. 班组配置
     */
    private static final List<String> childNodeLabel = Arrays.asList(TableNameDef.CLASSES_SCHEME, TableNameDef.CLASSES_CONFIG,
            TableNameDef.TEAM_GROUP_INFO);

    /**
     * 查询排班方案
     *
     * @param dto 条件
     * @return 排班方案
     */
    @Override
    public ResultWithTotal<List<SchedulingScheme>> pageQuery(SchedulingSchemeQueryDTO dto) {
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(TableNameDef.SCHEDULING_SCHEME);
        if (StringUtils.isNotBlank(dto.getKey())) {
            builder.like(TableColumnNameDef.COLUMN_NAME, dto.getKey());
        }
        if (Objects.nonNull(dto.getClassTeamType())) {
            builder.eq(TableColumnNameDef.CLASS_TEAM_TYPE, dto.getClassTeamType());
        }
        builder.leftJoin(childNodeLabel)
                .orderByDescending(TableColumnNameDef.COLUMN_ID);
        List<SchedulingScheme> schedulingSchemes = modelServiceUtils.queryWithRedis(builder, SchedulingScheme.class);
        Integer index = (dto.getIndex() - 1) * dto.getSize();
        List<SchedulingScheme> schedulingSchemesPage = PageUtils.pageByList(schedulingSchemes, index, dto.getSize());
        return ResultWithTotal.ok(schedulingSchemesPage, schedulingSchemes.size());
    }

    /**
     * 查询所有排班方案以及关联班次班组
     *
     * @return 排班方案以及关联班次班组
     */
    @Override
    public List<SchedulingScheme> queryAll() {
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(TableNameDef.SCHEDULING_SCHEME);
        builder.leftJoin(childNodeLabel).orderByDescending(TableColumnNameDef.COLUMN_ID);
        return modelServiceUtils.queryWithRedis(builder, SchedulingScheme.class);
    }

    /**
     * 根据id查询关联节点
     *
     * @param id 排版方案id
     * @return 排版方案和班次班组
     */
    @Override
    public SchedulingScheme queryAssociationNodeById(Long id) {
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(TableNameDef.SCHEDULING_SCHEME)
                .eq(TableColumnNameDef.COLUMN_ID, id)
                .leftJoin(childNodeLabel)
                .orderByDescending(TableColumnNameDef.COLUMN_ID);
        List<SchedulingScheme> schedulingSchemes = modelServiceUtils.queryWithRedis(builder, SchedulingScheme.class);
        if (CollectionUtils.isNotEmpty(schedulingSchemes)) {
            return schedulingSchemes.get(0);
        }
        return null;
    }

    /**
     * 查询生产类型排班方案
     *
     * @return 生产类型排班方案
     */
    @Override
    public List<SchedulingScheme> queryProduceSchedulingScheme(Integer classTeamType) {
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(TableNameDef.SCHEDULING_SCHEME);
        builder.eq(TableColumnNameDef.CLASS_TEAM_TYPE, classTeamType);
        builder.leftJoin(childNodeLabel).orderByDescending(TableColumnNameDef.COLUMN_ID);
        return modelServiceUtils.queryWithRedis(builder, SchedulingScheme.class);
    }
}