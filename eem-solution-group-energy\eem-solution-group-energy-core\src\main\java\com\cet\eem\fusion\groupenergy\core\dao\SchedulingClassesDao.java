package com.cet.eem.fusion.groupenergy.core.dao;

import com.cet.eem.dao.BaseModelDao;
import com.cet.piem.entity.classes.SchedulingClasses;

import java.util.List;

/**
 * 排班班次dao
 *
 * <AUTHOR>
 */
public interface SchedulingClassesDao extends BaseModelDao<SchedulingClasses> {

    /**
     * 根据班次id查询排班班次
     *
     * @param classesConfigIds 班次id
     * @return 排班班次
     */
    List<SchedulingClasses> queryByClassesConfig(List<Long> classesConfigIds);


    /**
     * 根据班组查询排班班次
     *
     * @param teamGroupIds 班次id
     * @return 排班班次
     */
    List<SchedulingClasses> queryByTeamGroup(List<Long> teamGroupIds);

    /**
     * 根据时间范围查询排班班次
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 排版班次
     */
    List<SchedulingClasses> queryByTimeRange(Long startTime, Long endTime,Long schedulingSchemeId);

    /**
     * 根据时间范围查询排班班次
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 排版班次
     */
    List<SchedulingClasses> queryByTimeRange(Long startTime, Long endTime);
}
