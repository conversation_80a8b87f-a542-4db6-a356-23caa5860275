<?xml version='1.0' encoding='cp936'?>
<problems><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Result</description>
  <highlighted_element>Result</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;TeamGroupEnergyInfoVO&gt; queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Result</description>
  <highlighted_element>Result</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;TeamGroupEnergyInfoVO&gt; queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>18</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;TeamGroupEnergyInfoVO&gt; queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>79</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>43</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;List&lt;TeamGroupEnergyHistogramVO&gt;&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Result</description>
  <highlighted_element>Result</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>43</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;List&lt;TeamGroupEnergyHistogramVO&gt;&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>43</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;List&lt;TeamGroupEnergyHistogramVO&gt;&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>95</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>49</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;List&lt;ClassesEnergyInfoVO&gt;&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Result</description>
  <highlighted_element>Result</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>49</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;List&lt;ClassesEnergyInfoVO&gt;&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>49</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;List&lt;ClassesEnergyInfoVO&gt;&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>84</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;ClassesEnergyInfoVO&gt; queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Result</description>
  <highlighted_element>Result</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;ClassesEnergyInfoVO&gt; queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>18</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;ClassesEnergyInfoVO&gt; queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>61</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController Result&lt;List&lt;Map&lt;String,Object&gt;&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Result</description>
  <highlighted_element>Result</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>6</length>
</problem>
</problems>