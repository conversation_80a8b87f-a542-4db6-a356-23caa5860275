package com.cet.eem.fusion.transformer.core.common.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * @ClassName : EquipmentStatus
 * <AUTHOR> yangy
 * @Date: 2022-03-16 11:39
 */
public enum EquipmentStatus {
    /**
     * 负载率
     */
    LOADRATE("负载率", 1),
    LOSSRATE("损耗率", 2),
    AVERAGEPOWERFACTOR("平均功率因数", 3),
    OPERATIONRATE("运行率", 4);

    String desc;
    Integer type;

    EquipmentStatus(String desc, Integer type) {
        this.desc = desc;
        this.type = type;
    }

    public static EquipmentStatus getEnum(Integer type) {
        return Stream.of(EquipmentStatus.values()).filter(item -> Objects.equals(item.type, type)).findFirst()
                .orElse(null);
    }
}
