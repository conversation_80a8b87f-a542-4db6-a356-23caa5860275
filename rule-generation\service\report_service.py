"""
报告服务
封装报告生成相关的业务逻辑
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

from config.settings import REPORT_CONFIG
from service.java_parser_service import JavaParserService

logger = logging.getLogger(__name__)


class ReportService:
    """报告服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.output_dir = REPORT_CONFIG["output_dir"]
        self.max_results = REPORT_CONFIG["max_results_per_file"]
        self.similarity_threshold = REPORT_CONFIG["similarity_threshold"]
        self.include_code_preview = REPORT_CONFIG["include_code_preview"]
        self.code_preview_length = REPORT_CONFIG["code_preview_length"]
        self.java_parser = JavaParserService()
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_search_report(self, search_results: Dict[str, Any], output_filename: str = None) -> str:
        """
        生成搜索报告
        
        Args:
            search_results (Dict[str, Any]): 搜索结果数据
            output_filename (str): 输出文件名，默认自动生成
            
        Returns:
            str: 报告文件路径
        """
        if output_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"匹配结果_{timestamp}.md"

        report_path = os.path.join(self.output_dir, output_filename)

        # 调试信息：记录报告生成调用
        logger.info(f"开始生成报告: {output_filename}")
        logger.info(f"报告路径: {report_path}")

        # 检查文件是否已存在
        if os.path.exists(report_path):
            logger.warning(f"报告文件已存在，将被覆盖: {report_path}")
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                self._write_report_header(f, search_results)
                
                if search_results.get("success", False):
                    if "batch_results" in search_results:
                        self._write_batch_results(f, search_results)
                    else:
                        self._write_single_result(f, search_results)
                else:
                    self._write_error_section(f, search_results)
                
                self._write_report_footer(f)
            
            logger.info(f"报告生成成功: {report_path}")

            # 同时生成JSON格式的结果
            json_path = self._generate_json_report(search_results, output_filename)
            logger.info(f"JSON结果生成成功: {json_path}")

            return report_path
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            raise

    def _generate_json_report(self, search_results: Dict[str, Any], output_filename: str = None) -> str:
        """
        生成JSON格式的检索结果

        Args:
            search_results (Dict[str, Any]): 搜索结果数据
            output_filename (str): 输出文件名，默认自动生成

        Returns:
            str: JSON文件路径
        """
        if output_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_filename = f"匹配结果_{timestamp}.json"
        else:
            # 将.md扩展名替换为.json
            if output_filename.endswith('.md'):
                json_filename = output_filename[:-3] + '.json'
            else:
                json_filename = output_filename + '.json'

        json_path = os.path.join(self.output_dir, json_filename)

        try:
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(search_results, f, ensure_ascii=False, indent=2)

            return json_path

        except Exception as e:
            logger.error(f"生成JSON报告失败: {e}")
            raise

    def _analyze_results_summary(self, search_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析检索结果，生成汇总信息

        Args:
            search_results (Dict[str, Any]): 搜索结果数据

        Returns:
            Dict[str, Any]: 汇总分析结果
        """
        batch_results = search_results.get("batch_results", [])

        # 第一部分：只有一个超过阈值的类的集合清单
        single_match_classes = []

        # 第二部分：有多个超过阈值的匹配结果的部分
        multiple_match_classes = []

        # 第三部分：没有匹配到的类的清单
        no_match_classes = []

        for batch_result in batch_results:
            if not batch_result.get("success", False):
                # 检索失败的类
                query_file = batch_result.get("query_file", "")
                class_name = self._extract_class_name_from_path(query_file)
                no_match_classes.append({
                    "class_name": class_name,
                    "file_path": query_file,
                    "error": batch_result.get("error", "检索失败")
                })
                continue

            results = batch_result.get("results", [])
            query_file = batch_result.get("query_file", "")
            class_name = self._extract_class_name_from_path(query_file)

            # 筛选超过阈值的匹配
            high_similarity_results = [
                r for r in results
                if r.get("similarity_score", 0) >= self.similarity_threshold
            ]

            if len(high_similarity_results) == 0:
                # 没有超过阈值的匹配
                no_match_classes.append({
                    "class_name": class_name,
                    "file_path": query_file,
                    "reason": "无高相似度匹配"
                })
            elif len(high_similarity_results) == 1:
                # 只有一个超过阈值的匹配
                match = high_similarity_results[0]
                package_name = self._extract_package_name("", match.get("code_content", ""))
                full_class_path = f"{package_name}.{match.get('class_name', '')}" if package_name != "未知包名" else match.get('class_name', '')

                single_match_classes.append({
                    "class_name": class_name,
                    "file_path": query_file,
                    "matched_class": match.get("class_name", ""),
                    "full_class_path": full_class_path,
                    "similarity_score": match.get("similarity_score", 0)
                })
            else:
                # 多个超过阈值的匹配
                matched_classes = []
                for match in high_similarity_results:
                    package_name = self._extract_package_name("", match.get("code_content", ""))
                    full_class_path = f"{package_name}.{match.get('class_name', '')}" if package_name != "未知包名" else match.get('class_name', '')

                    matched_classes.append({
                        "class_name": match.get("class_name", ""),
                        "full_class_path": full_class_path,
                        "similarity_score": match.get("similarity_score", 0)
                    })

                multiple_match_classes.append({
                    "class_name": class_name,
                    "file_path": query_file,
                    "match_count": len(high_similarity_results),
                    "matched_classes": matched_classes
                })

        return {
            "single_match_classes": single_match_classes,
            "multiple_match_classes": multiple_match_classes,
            "no_match_classes": no_match_classes,
            "summary_stats": {
                "single_match_count": len(single_match_classes),
                "multiple_match_count": len(multiple_match_classes),
                "no_match_count": len(no_match_classes),
                "total_classes": len(single_match_classes) + len(multiple_match_classes) + len(no_match_classes)
            }
        }

    def _extract_class_name_from_path(self, file_path: str) -> str:
        """
        从文件路径中提取类名

        Args:
            file_path (str): 文件路径

        Returns:
            str: 类名
        """
        filename = os.path.basename(file_path)
        if filename.endswith('.java'):
            return filename[:-5]  # 去掉.java扩展名
        return filename

    def _write_analysis_summary(self, f, analysis: Dict[str, Any]):
        """
        写入汇总分析结果

        Args:
            f: 文件对象
            analysis (Dict[str, Any]): 分析结果
        """
        f.write("### 结果汇总分析\n\n")

        stats = analysis["summary_stats"]
        total_classes = stats['total_classes']

        # 计算占比
        single_percentage = (stats['single_match_count'] / total_classes * 100) if total_classes > 0 else 0
        multiple_percentage = (stats['multiple_match_count'] / total_classes * 100) if total_classes > 0 else 0
        no_match_percentage = (stats['no_match_count'] / total_classes * 100) if total_classes > 0 else 0

        f.write(f"- **总类数**: {stats['total_classes']}\n")
        f.write(f"- **单一匹配类数**: {stats['single_match_count']} (占比{single_percentage:.1f}%)\n")
        f.write(f"- **多重匹配类数**: {stats['multiple_match_count']} (占比{multiple_percentage:.1f}%)\n")
        f.write(f"- **无匹配类数**: {stats['no_match_count']} (占比{no_match_percentage:.1f}%)\n\n")

        # 第一部分：只有一个超过阈值的类
        single_matches = analysis["single_match_classes"]
        single_count = len(single_matches)
        single_percentage = (single_count / stats['total_classes'] * 100) if stats['total_classes'] > 0 else 0

        f.write(f"#### 1. 单一高相似度匹配类 ({single_count}个，占比{single_percentage:.1f}%)\n\n")

        if single_matches:
            f.write("以下类只找到一个超过阈值的匹配结果：\n\n")

            for item in single_matches:
                f.write(f"- **{item['class_name']}** → `{item['full_class_path']}` (相似度: {item['similarity_score']:.3f})\n")
            f.write("\n")
        else:
            f.write("无单一高相似度匹配类。\n\n")

        # 第二部分：有多个超过阈值的匹配结果
        multiple_matches = analysis["multiple_match_classes"]
        multiple_count = len(multiple_matches)
        multiple_percentage = (multiple_count / stats['total_classes'] * 100) if stats['total_classes'] > 0 else 0

        f.write(f"#### 2. 多重高相似度匹配类 ({multiple_count}个，占比{multiple_percentage:.1f}%)\n\n")

        if multiple_matches:
            f.write("以下类找到多个超过阈值的匹配结果：\n\n")

            for item in multiple_matches:
                f.write(f"**{item['class_name']}** (找到 {item['match_count']} 个匹配):\n")
                for match in item['matched_classes']:
                    f.write(f"  - `{match['full_class_path']}` (相似度: {match['similarity_score']:.3f})\n")
                f.write("\n")
        else:
            f.write("无多重高相似度匹配类。\n\n")

        # 第三部分：没有匹配到的类
        no_matches = analysis["no_match_classes"]
        no_match_count = len(no_matches)
        no_match_percentage = (no_match_count / stats['total_classes'] * 100) if stats['total_classes'] > 0 else 0

        f.write(f"#### 3. 无高相似度匹配类 ({no_match_count}个，占比{no_match_percentage:.1f}%)\n\n")

        if no_matches:
            f.write("以下类未找到超过阈值的匹配结果：\n\n")

            for item in no_matches:
                reason = item.get('reason', item.get('error', '未知原因'))
                f.write(f"- **{item['class_name']}** ({reason})\n")
            f.write("\n")
        else:
            f.write("所有类都找到了高相似度匹配。\n\n")
    
    def _write_report_header(self, f, search_results: Dict[str, Any]):
        """写入报告头部"""
        f.write("# Java代码相似性检索报告\n\n")
        f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        if "batch_results" in search_results:
            f.write("## 批量检索概览\n\n")
            f.write(f"- **总文件数**: {search_results.get('total_files', 0)}\n")
            f.write(f"- **成功检索**: {search_results.get('successful_searches', 0)}\n")
            f.write(f"- **失败检索**: {search_results.get('failed_searches', 0)}\n")
            f.write(f"- **相似度阈值**: {self.similarity_threshold}\n\n")
        else:
            f.write("## 单文件检索概览\n\n")
            f.write(f"- **查询文件**: {search_results.get('query_file', 'N/A')}\n")
            f.write(f"- **检索结果数**: {search_results.get('total_results', 0)}\n")
            f.write(f"- **相似度阈值**: {self.similarity_threshold}\n\n")
    
    def _write_single_result(self, f, search_results: Dict[str, Any]):
        """写入单个文件的检索结果"""
        f.write("## 检索结果\n\n")
        
        results = search_results.get("results", [])
        if not results:
            f.write("未找到相似代码。\n\n")
            return
        
        for result in results:
            similarity_score = result.get("similarity_score", 0)
            
            f.write(f"### 排名 {result.get('rank', 'N/A')}\n\n")
            f.write(f"- **类名**: `{result.get('class_name', 'N/A')}`\n")
            f.write(f"- **相似度分数**: {similarity_score:.4f}\n")
            f.write(f"- **距离**: {result.get('distance', 'N/A'):.4f}\n")
            
            # 相似度等级
            if similarity_score >= 0.9:
                level = "🔴 极高相似"
            elif similarity_score >= 0.8:
                level = "🟠 高相似"
            elif similarity_score >= 0.7:
                level = "🟡 中等相似"
            else:
                level = "🟢 低相似"
            
            f.write(f"- **相似度等级**: {level}\n\n")
            
            # 代码预览
            if self.include_code_preview and result.get("code_content"):
                code_content = result["code_content"]
                if len(code_content) > self.code_preview_length:
                    preview = code_content[:self.code_preview_length] + "..."
                else:
                    preview = code_content
                
                f.write("**代码预览**:\n\n")
                f.write("```java\n")
                f.write(preview)
                f.write("\n```\n\n")
            
            f.write("---\n\n")
    
    def _extract_package_name(self, file_path: str, code_content: str) -> str:
        """
        从代码内容中提取包名

        Args:
            file_path (str): 文件路径（已废弃，保留参数兼容性）
            code_content (str): 代码内容

        Returns:
            str: 包名
        """
        try:
            # 使用Java解析服务提取包名
            return self.java_parser.extract_package_name(code_content)
        except Exception:
            return "未知包名"

    def _write_batch_results(self, f, search_results: Dict[str, Any]):
        """写入批量检索结果"""
        f.write("## 批量检索结果\n\n")

        batch_results = search_results.get("batch_results", [])
        if not batch_results:
            f.write("无检索结果。\n\n")
            return

        # 统计信息
        high_similarity_count = 0
        total_matches = 0

        for batch_result in batch_results:
            if batch_result.get("success", False):
                results = batch_result.get("results", [])
                for result in results:
                    total_matches += 1
                    if result.get("similarity_score", 0) >= self.similarity_threshold:
                        high_similarity_count += 1

        f.write(f"### 统计摘要\n\n")
        f.write(f"- **总匹配数**: {total_matches}\n")
        f.write(f"- **高相似度匹配**: {high_similarity_count}\n")
        f.write(f"- **高相似度比例**: {high_similarity_count/total_matches*100:.1f}%\n\n" if total_matches > 0 else "- **高相似度比例**: 0%\n\n")

        # 新增汇总分析
        analysis = self._analyze_results_summary(search_results)
        self._write_analysis_summary(f, analysis)

        # 详细结果
        f.write("### 详细结果\n\n")

        for i, batch_result in enumerate(batch_results, 1):
            query_file = batch_result.get("query_file", "N/A")
            filename = os.path.basename(query_file)

            # 提取查询文件的类名（去掉.java扩展名）
            query_class_name = filename.replace('.java', '') if filename.endswith('.java') else filename

            f.write(f"#### {i}. {query_class_name}\n\n")
            f.write(f"**文件路径**: `{query_file}`\n\n")

            if not batch_result.get("success", False):
                f.write(f"❌ **检索失败**: {batch_result.get('error', '未知错误')}\n\n")
                continue

            results = batch_result.get("results", [])
            if not results:
                f.write("未找到相似代码。\n\n")
                continue

            # 生成表格
            f.write("| 匹配类名 | 完整类路径 | 相似度 | 超过阈值 |\n")
            f.write("|---------|-----------|--------|----------|\n")

            for result in results:
                similarity_score = result.get("similarity_score", 0)
                class_name = result.get("class_name", "N/A")
                code_content = result.get("code_content", "")

                # 提取包名
                package_name = self._extract_package_name("", code_content)

                # 构建完整类路径
                if package_name != "未知包名":
                    full_class_path = f"{package_name}.{class_name}"
                else:
                    full_class_path = class_name

                # 判断是否超过阈值
                exceeds_threshold = "✅" if similarity_score >= self.similarity_threshold else "❌"

                f.write(f"| {class_name} | `{full_class_path}` | {similarity_score:.3f} | {exceeds_threshold} |\n")

            f.write("\n")
    
    def _write_error_section(self, f, search_results: Dict[str, Any]):
        """写入错误信息"""
        f.write("## 错误信息\n\n")
        f.write(f"❌ **检索失败**: {search_results.get('error', '未知错误')}\n\n")
    
    def _write_report_footer(self, f):
        """写入报告尾部"""
        f.write("---\n\n")
        f.write("*报告由Java代码检索工具自动生成*\n")
    
    def generate_summary_report(self, multiple_reports: List[str]) -> str:
        """
        生成多个报告的汇总报告
        
        Args:
            multiple_reports (List[str]): 报告文件路径列表
            
        Returns:
            str: 汇总报告文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_filename = f"summary_report_{timestamp}.md"
        summary_path = os.path.join(self.output_dir, summary_filename)
        
        try:
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write("# Java代码检索汇总报告\n\n")
                f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(f"**包含报告数**: {len(multiple_reports)}\n\n")
                
                f.write("## 报告列表\n\n")
                for i, report_path in enumerate(multiple_reports, 1):
                    report_name = os.path.basename(report_path)
                    f.write(f"{i}. [{report_name}]({report_path})\n")
                
                f.write("\n---\n\n")
                f.write("*汇总报告由Java代码检索工具自动生成*\n")
            
            logger.info(f"汇总报告生成成功: {summary_path}")
            return summary_path
            
        except Exception as e:
            logger.error(f"生成汇总报告失败: {e}")
            raise
