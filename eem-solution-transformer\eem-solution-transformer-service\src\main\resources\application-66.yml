web_ip: ************
db_ip: ************

spring:
  redis:
    database: 1
    password: Ceiec4567%%
    port: 26379
  rabbitmq:
    port: 5673
    username: eem
    password: Ceiec4567%%
  security:
    user:
      name: eem
      password: Ceiec4567%%

cet:
  i18n:
    dir: D:\WorkPlace\energy-service_5.0\energy-base-fusion\resources\i18n
  eem:
    base:
      debug: true
swagger:
  enabled: true

eureka:
  client:
    service-url:
      defaultZone: http://user:CETgroup!123@${web_ip}:1001/eureka/  # Eureka服务地址
  instance:
    prefer-ip-address: true # 使用IP地址注册
    ip-address: ************