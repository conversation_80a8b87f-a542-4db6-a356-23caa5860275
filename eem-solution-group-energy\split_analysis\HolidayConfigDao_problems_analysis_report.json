{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao", "problems_count": 2, "analysis_results": [{"class_name": "BaseModelDao", "line": [3, 13], "description": "未解析的引用 BaseModelDao", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.BaseModelDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "HolidayConfig", "line": [4, 13, 21], "description": "未解析的引用 HolidayConfig", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.HolidayConfig"], "suggest": "请使用新的类路径替换"}]}