## 配置文件无修改
- TransformerConfigAutoConfiguration.java
- EemFusionTransformerBeanNameGenerator.java
- EemSolutionTransformerSwaggerConfig.java
- PluginConfiguration.java
## 枚举类无修改
- EquipmentStatus.java
- FunctionEnum.java
- TransformerlevelEnum.java

## Controller层
- TransformerAnalysisController.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "Result"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.Result;"
calling_class: "TransformerAnalysisController"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["9"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "GlobalInfoUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.util.GlobalInfoUtils;"
calling_class: "TransformerAnalysisController"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["8"]

问题3-问题7
error_type: "GlobalInfoUtils.getProjectId废弃"
error_code: "工具类废弃和替换详细方案"
missing_class: ""
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.util.GlobalInfoUtils;"
calling_class: "TransformerAnalysisController"
calling_method: "queryEquipmentMonitorInfo"
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["41"]

问题8-问题12
error_type: "Result不允许作为Controller返回实体类"
error_code: "数据访问层变更详细方案"
missing_class: ""
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.Result;"
calling_class: "TransformerAnalysisController"
calling_method: "queryEquipmentMonitorInfo"
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["40"]
```
- TransformerOverviewController.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "Result"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.Result;"
calling_class: "TransformerOverviewController"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["9"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "GlobalInfoUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.util.GlobalInfoUtils;"
calling_class: "TransformerOverviewController"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["8"]

问题3-问题7
error_type: "GlobalInfoUtils.getProjectId废弃"
error_code: "工具类废弃和替换详细方案"
missing_class: ""
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.util.GlobalInfoUtils;"
calling_class: "TransformerOverviewController"
calling_method: "getOverviewData"
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["35"]

问题8-问题12
error_type: "Result不允许作为Controller返回实体类"
error_code: "数据访问层变更详细方案"
missing_class: ""
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.Result;"
calling_class: "TransformerOverviewController"
calling_method: "getOverviewData"
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["34"]
```
- PowerTransformerDao.java
- PowerTransformerDaoImpl.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "QueryCondition"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.base.QueryCondition;"
calling_class: "PowerTransformerDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["3"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "ModelServiceUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.ModelServiceUtils;"
calling_class: "PowerTransformerDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["4"]

问题3
error_type: "QueryConditionBuilder废弃，需要用ParentQueryConditionBuilder重构"
error_code: "数据访问层变更详细方案"
missing_class: "QueryConditionBuilder"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.QueryConditionBuilder;"
calling_class: "PowerTransformerDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["5"]

问题4
error_type: "cannot find symbol"
error_code: ""
missing_class: "CollectionUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.toolkit.CollectionUtils;"
calling_class: "PowerTransformerDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["5"]

问题5
error_type: "cannot find symbol"
error_code: ""
missing_class: "NodeLabelDef"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "PowerTransformerDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["29"]

问题6
error_type: "cannot find symbol"
error_code: ""
missing_class: "NodeLabelDef"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "PowerTransformerDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["29"]
```
- TransformerindexDataDao.java
- TransformerindexDataDaoImpl.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "QueryCondition"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.base.QueryCondition;"
calling_class: "TransformerindexDataDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["31"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "ModelServiceUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.ModelServiceUtils;"
calling_class: "TransformerindexDataDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["22"]

问题3
error_type: "QueryConditionBuilder废弃，需要用ParentQueryConditionBuilder重构"
error_code: "数据访问层变更详细方案"
missing_class: "QueryConditionBuilder"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.QueryConditionBuilder;"
calling_class: "TransformerindexDataDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["31"]

问题4
error_type: "cannot find symbol"
error_code: ""
missing_class: "Constant"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerindexDataDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["31"]

问题5
error_type: "cannot find symbol"
error_code: ""
missing_class: "EemPoiRecord"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerindexDataDaoImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["59"]
```
- TransformerConstantDef.java
- Event.java
- Quantity.java
- RadarChartInfo.java
- TimeValue.java
- EquipmentCondition.java
- EquipmentForm.java
- LoadRateParam.java
- Operation.java
- PowerTransformerDto.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "PowerTransformerVo"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "PowerTransformerDto"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["19"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "Project"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "PowerTransformerDto"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["37"]
```
- ProjectDto.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "Project"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "ProjectDto"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["4"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "NodeLabelDef"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "ProjectDto"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["3"]
```
- TransformerindexData.java
```
问题1
error_type: "实体类继承BaseEntity需要修改为EntityWithName"
error_code: ""
missing_class: "BaseEntity"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerindexData"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["11"]
```
- EquipmentMonitorVo.java
- HistoricalLoadVo.java
- LoadInfoVo.java
- LoadRateVo.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "TimeValue"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "LoadRateVo"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["18"]
```
- OverviewDataVo.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "Operation"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "OverviewDataVo"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["14"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "Quantity"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "OverviewDataVo"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["16"]

问题3
error_type: "cannot find symbol"
error_code: ""
missing_class: "Event"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "OverviewDataVo"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["18"]
```
- SectionVo.java
- VoltageSideMonitorVo.java
- TransformerAnalysisService.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "RadarChartInfo"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerAnalysisService"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["48"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "BaseVo"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerAnalysisService"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["67"]

问题3
error_type: "cannot find symbol"
error_code: ""
missing_class: "PointNode"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerAnalysisService"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["67"]

问题4
error_type: "cannot find symbol"
error_code: ""
missing_class: "LinkNode"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerAnalysisService"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["67"]

问题5
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityDataBatchSearchVo"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerAnalysisService"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["67"]
```
- TransformerindexDataService.java
- TransformerOverviewService.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "OverviewDataVo"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerOverviewService"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["15"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "EquipmentCondition"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerOverviewService"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["44"]

问题3
error_type: "cannot find symbol"
error_code: ""
missing_class: "EquipmentForm"
missing_method: ""
missing_field: ""
import_statements:
    - ""
calling_class: "TransformerOverviewService"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["44"]
```
- TransformerTaskService.java
- TransformerAnalysisServiceImpl.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "NodeDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.dao.node.NodeDao;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["3"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "PipeNetworkConnectionModelDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["4"]

问题3
error_type: "cannot find symbol"
error_code: ""
missing_class: "FrequencyDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.def.quantity.FrequencyDef;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["5"]

问题4
error_type: "cannot find symbol"
error_code: ""
missing_class: "FrequencyDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.def.quantity.PhasorDef;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["6"]

问题5
error_type: "cannot find symbol"
error_code: ""
missing_class: "FrequencyDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["7"]

问题6
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityTypeDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["8"]

问题7
error_type: "cannot find symbol"
error_code: ""
missing_class: "PipeNetworkConnectionModel"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["10"]

问题8
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityAggregationData"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["11"]

问题9
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityObject"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["12"]

问题10
error_type: "cannot find symbol"
error_code: ""
missing_class: "PowerTransformerVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["13"]

问题11
error_type: "cannot find symbol"
error_code: ""
missing_class: "LinkNode"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.topology.vo.LinkNode;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["14"]

问题12
error_type: "cannot find symbol"
error_code: ""
missing_class: "PointNode"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.topology.vo.PointNode;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["15"]

问题13
error_type: "cannot find symbol"
error_code: ""
missing_class: "CommonUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.CommonUtils;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["16"]

问题14
error_type: "cannot find symbol"
error_code: ""
missing_class: "AggregationType"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.TransformerConstantDef.AggregationType;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["17"]

问题15
error_type: "cannot find symbol"
error_code: ""
missing_class: "EnergyTypeDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.TransformerConstantDef.EnergyTypeDef;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["18"]

问题16
error_type: "cannot find symbol"
error_code: ""
missing_class: "EnumDataTypeId"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.TransformerConstantDef.EnumDataTypeId;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["19"]

问题17
error_type: "cannot find symbol"
error_code: ""
missing_class: "EnumOperationType"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.TransformerConstantDef.EnumOperationType;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["20"]

问题18
error_type: "cannot find symbol"
error_code: ""
missing_class: "ColumnDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.definition.ColumnDef;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["21"]

问题19
error_type: "cannot find symbol"
error_code: ""
missing_class: "NodeLabelDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.definition.NodeLabelDef;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["22"]

问题20
error_type: "cannot find symbol"
error_code: ""
missing_class: "BaseVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.BaseVo;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["23"]

问题21
error_type: "cannot find symbol"
error_code: ""
missing_class: "DataLogData"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.datalog.DataLogData;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["24"]

问题22
error_type: "cannot find symbol"
error_code: ""
missing_class: "TrendDataVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.datalog.TrendDataVo;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["25"]

问题23
error_type: "cannot find symbol"
error_code: ""
missing_class: "TrendDataVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["26"]

问题24
error_type: "cannot find symbol"
error_code: ""
missing_class: "RealTimeValueVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.realtime.RealTimeValueVo;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["27"]

问题25
error_type: "cannot find symbol"
error_code: ""
missing_class: "JsonTransferUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.parse.JsonTransferUtils;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["28"]

问题26
error_type: "cannot find symbol"
error_code: ""
missing_class: "TimeUtil"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.utils.TimeUtil;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["29"]

问题26
error_type: "cannot find symbol"
error_code: ""
missing_class: "ConditionBlock"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.base.ConditionBlock;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["30"]

问题27
error_type: "cannot find symbol"
error_code: ""
missing_class: "QueryCondition"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.base.QueryCondition;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["31"]

问题28
error_type: "cannot find symbol"
error_code: ""
missing_class: "BaseEntity"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.model.BaseEntity;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["32"]

问题28
error_type: "cannot find symbol"
error_code: ""
missing_class: "ModelServiceUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.ModelServiceUtils;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["33"]

问题29
error_type: "cannot find symbol"
error_code: ""
missing_class: "QueryConditionBuilder"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.QueryConditionBuilder;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["34"]

问题30
error_type: "cannot find symbol"
error_code: ""
missing_class: "Topology1Service"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.node.service.Topology1Service;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["37"]

问题31
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityAggregationDataDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.dao.QuantityAggregationDataDao;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["38"]

问题32
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityObjectDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.dao.QuantityObjectDao;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["39"]

问题33
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityDataBatchSearchVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["40"]

问题34
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantitySearchVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.model.quantity.QuantitySearchVo;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["41"]

问题35
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityManageService"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.service.QuantityManageService;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["42"]

问题36-39
error_type: "Topology1Service废弃"
error_code: ""
missing_class: "Topology1Service"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.node.service.Topology1Service;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["114","128","166","272"]

问题40-45
error_type: "QuantityManageService废弃"
error_code: ""
missing_class: "QuantityManageService"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.service.QuantityManageService;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["700","839","966","1023","1033","1277"]

问题46-49
error_type: "QuantityObjectDao废弃"
error_code: ""
missing_class: "QuantityObjectDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.dao.QuantityObjectDao;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["174","298","419","468"]

问题50-51
error_type: "PipeNetworkConnectionModelDao废弃"
error_code: ""
missing_class: "PipeNetworkConnectionModelDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["416","465"]

问题52-53
error_type: "QueryConditionBuilder废弃，需要考虑用ParentQueryConditionBuilder重构"
error_code: ""
missing_class: "QueryConditionBuilder"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.QueryConditionBuilder;"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["756","1219"]
```
- TransformerindexDataServiceImpl.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "EemPoiRecordDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["3"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "Project"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.organization.Project;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["4"]

问题3
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityAggregationData"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["5"]

问题4
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityObject"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["6"]

问题5
error_type: "cannot find symbol"
error_code: ""
missing_class: "EemPoiRecord"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["7"]

问题6
error_type: "cannot find symbol"
error_code: ""
missing_class: "LinkNode"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.topology.vo.LinkNode;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["8"]

问题7
error_type: "cannot find symbol"
error_code: ""
missing_class: "PointNode"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.topology.vo.PointNode;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["9"]

问题8
error_type: "cannot find symbol"
error_code: ""
missing_class: "ProjectService"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.service.ProjectService;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["10"]

问题9
error_type: "cannot find symbol"
error_code: ""
missing_class: "CommonUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.CommonUtils;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["11"]

问题10
error_type: "cannot find symbol"
error_code: ""
missing_class: "AggregationType"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.constant.AggregationType;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["12"]

问题11
error_type: "cannot find symbol"
error_code: ""
missing_class: "EnergyTypeDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.constant.EnergyTypeDef;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["13"]

问题12
error_type: "cannot find symbol"
error_code: ""
missing_class: "EnumOperationType"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.constant.EnumOperationType;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["14"]

问题13
error_type: "cannot find symbol"
error_code: ""
missing_class: "PoiTypeEnum"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.constant.PoiTypeEnum;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["15"]

问题14
error_type: "cannot find symbol"
error_code: ""
missing_class: "NodeLabelDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.definition.NodeLabelDef;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["16"]

问题15
error_type: "cannot find symbol"
error_code: ""
missing_class: "BaseVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.BaseVo;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["17"]

问题16
error_type: "cannot find symbol"
error_code: ""
missing_class: "DataLogData"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.datalog.DataLogData;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["18"]

问题17
error_type: "cannot find symbol"
error_code: ""
missing_class: "AggregationCycle"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["19"]

问题18
error_type: "cannot find symbol"
error_code: ""
missing_class: "JsonTransferUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.parse.JsonTransferUtils;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["20"]

问题19
error_type: "cannot find symbol"
error_code: ""
missing_class: "TimeUtil"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.common.utils.TimeUtil;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["21"]

问题20
error_type: "cannot find symbol"
error_code: ""
missing_class: "TransformerindexDataService"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.fusion.transformer.core.service.TransformerindexDataService;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["22"]

问题21
error_type: "cannot find symbol"
error_code: ""
missing_class: "QueryCondition"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.base.QueryCondition;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["23"]

问题22
error_type: "cannot find symbol"
error_code: ""
missing_class: "ModelServiceUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.ModelServiceUtils;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["24"]

问题23
error_type: "cannot find symbol"
error_code: ""
missing_class: "QueryConditionBuilder"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.QueryConditionBuilder;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["25"]

问题24
error_type: "cannot find symbol"
error_code: ""
missing_class: "Topology1Service"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.node.service.Topology1Service;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["27"]

问题25
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityAggregationDataDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.dao.QuantityAggregationDataDao;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["28"]

问题26
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityObjectDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.dao.QuantityObjectDao;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["29"]

问题27
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityDataBatchSearchVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["30"]

问题28
error_type: "cannot find symbol"
error_code: ""
missing_class: "TransformerindexDataPOService"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.fusion.transformer.core.service.TransformerindexDataPOService;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["31"]

问题29
error_type: "cannot find symbol"
error_code: ""
missing_class: "CollectionUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.toolkit.CollectionUtils;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["32"]

问题30
error_type: "cannot find symbol"
error_code: ""
missing_class: "PowerTransformerDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.transformer.dao.PowerTransformerDao;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["33"]

问题31
error_type: "cannot find symbol"
error_code: ""
missing_class: "TransformerindexDataPODao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.fusion.transformer.core.dao.TransformerindexDataPODao;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["34"]

问题32
error_type: "cannot find symbol"
error_code: ""
missing_class: "PowerTransformerDto"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.transformer.model.PowerTransformerDto;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["35"]

问题33
error_type: "cannot find symbol"
error_code: ""
missing_class: "TransformerindexDataPOPO"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.fusion.transformer.core.entity.po.TransformerindexDataPOPO;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["36"]

问题34
error_type: "cannot find symbol"
error_code: ""
missing_class: "Constant"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.transformer.model.constant.Constant;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["37"]

问题35
error_type: "cannot find symbol"
error_code: ""
missing_class: "DateUtil"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.transformer.model.util.DateUtil;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["38"]

问题36
error_type: "cannot find symbol"
error_code: ""
missing_class: "TransformerAnalysisServiceImpl"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.transformer.service.impl.TransformerAnalysisServiceImpl;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["39"]

问题37
error_type: "cannot find symbol"
error_code: ""
missing_class: "TransformerOverviewServiceImpl"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.transformer.service.impl.TransformerOverviewServiceImpl;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["40"]

问题38
error_type: "cannot find symbol"
error_code: ""
missing_class: "fixedGrouping"
missing_method: ""
missing_field: ""
import_statements:
    - "import static com.cet.eem.task.impl.TransformerTaskServiceImpl.fixedGrouping;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["55"]

问题39
error_type: "QuantityObjectDao废弃"
error_code: ""
missing_class: "QuantityObjectDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.dao.QuantityObjectDao;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["562"]

问题40
error_type: "Topology1Service废弃"
error_code: ""
missing_class: "Topology1Service"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.node.service.Topology1Service;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["593"]

问题41
error_type: "QueryConditionBuilder废弃，需要用ParentQueryConditionBuilder重构"
error_code: "数据访问层变更详细方案"
missing_class: "QueryConditionBuilder"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.QueryConditionBuilder;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["482"]

问题42
error_type: "DateUtil废弃，需要重构"
error_code: "数据访问层变更详细方案"
missing_class: "DateUtil"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.transformer.model.util.DateUtil;"
calling_class: "TransformerindexDataServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["638"]
```
- TransformerOverviewServiceImpl.java
```
问题1
error_type: "cannot find symbol"
error_code: ""
missing_class: "FrequencyDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.def.quantity.FrequencyDef;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["3"]

问题2
error_type: "cannot find symbol"
error_code: ""
missing_class: "PhasorDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.def.quantity.PhasorDef;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["4"]

问题3
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityCategoryDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["5"]

问题4
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityTypeDef"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["6"]

问题5
error_type: "cannot find symbol"
error_code: ""
missing_class: "PipeNetworkConnectionModel"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["7"]

问题5
error_type: "cannot find symbol"
error_code: ""
missing_class: "SystemEvent"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.event.SystemEvent;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["8"]

问题6
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityAggregationData"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["9"]

问题7
error_type: "cannot find symbol"
error_code: ""
missing_class: "QuantityObject"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["10"]

问题8
error_type: "cannot find symbol"
error_code: ""
missing_class: "PecEventExtendVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["11"]

问题9
error_type: "cannot find symbol"
error_code: ""
missing_class: "SystemEventWithText"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.ext.objective.event.SystemEventWithText;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["12"]

问题10
error_type: "cannot find symbol"
error_code: ""
missing_class: "ConnectionSearchVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.topology.vo.ConnectionSearchVo;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["13"]

问题11
error_type: "cannot find symbol"
error_code: ""
missing_class: "LinkNode"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.topology.vo.LinkNode;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["14"]

问题12
error_type: "cannot find symbol"
error_code: ""
missing_class: "PointNode"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.topology.vo.PointNode;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["15"]

问题13
error_type: "cannot find symbol"
error_code: ""
missing_class: "GlobalInfoUtils"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.util.GlobalInfoUtils;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["16"]

问题14
error_type: "cannot find symbol"
error_code: ""
missing_class: "SystemEventDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.energy.dao.SystemEventDao;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["17"]

问题15
error_type:"cannot find symbol"
error_code:""
missing_class:"SystemEventCountVo"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.bll.energy.model.event.SystemEventCountVo;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["18"]

问题16
error_type:"cannot find symbol"
error_code:""
missing_class:"AlarmEventService"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.bll.energy.service.event.AlarmEventService;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["19"]

问题17
error_type:"cannot find symbol"
error_code:""
missing_class:"CommonUtils"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.CommonUtils;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["20"]

问题18
error_type:"cannot find symbol"
error_code:""
missing_class:"AggregationType"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.constant.AggregationType;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["21"]

问题19
error_type:"cannot find symbol"
error_code:""
missing_class:"EnergyTypeDef"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.constant.EnergyTypeDef;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["22"]

问题20
error_type:"cannot find symbol"
error_code:""
missing_class:"EnumOperationType"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.constant.EnumOperationType;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["23"]

问题21
error_type:"cannot find symbol"
error_code:""
missing_class:"ColumnDef"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.definition.ColumnDef;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["24"]

问题22
error_type:"cannot find symbol"
error_code:""
missing_class:"NodeLabelDef"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.definition.NodeLabelDef;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["25"]

问题23
error_type:"cannot find symbol"
error_code:""
missing_class:""
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.model.*;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["26"]

问题24
error_type:"cannot find symbol"
error_code:""
missing_class:"DataLogData"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.model.datalog.DataLogData;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["27"]

问题25
error_type:"cannot find symbol"
error_code:""
missing_class:"AggregationCycle"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["28"]

问题26
error_type:"cannot find symbol"
error_code:""
missing_class:"RealTimeValue"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.model.realtime.RealTimeValue;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["29"]

问题27
error_type:"cannot find symbol"
error_code:""
missing_class:"JsonTransferUtils"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.parse.JsonTransferUtils;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["30"]

问题28
error_type:"cannot find symbol"
error_code:""
missing_class:"TimeUtil"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.common.utils.TimeUtil;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["31"]

问题29
error_type:"cannot find symbol"
error_code:""
missing_class:"ConfirmCountResult"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.event.model.analysis.ConfirmCountResult;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["32"]

问题30
error_type:"cannot find symbol"
error_code:""
missing_class:"EventCountSearchVo"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.event.model.expert.EventCountSearchVo;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["33"]

问题31
error_type:"cannot find symbol"
error_code:""
missing_class:"PecEventCountVo"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.event.model.pecevent.PecEventCountVo;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["34"]

问题32
error_type:"cannot find symbol"
error_code:""
missing_class:"PecEventService"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.event.service.PecEventService;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["35"]

问题33
error_type:"cannot find symbol"
error_code:""
missing_class:"ExpertAnalysisBffService"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.event.service.expert.ExpertAnalysisBffService;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["36"]

问题34
error_type:"cannot find symbol"
error_code:""
missing_class:"PecCoreEventBffService"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.event.service.expert.PecCoreEventBffService;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["37"]

问题35
error_type:"cannot find symbol"
error_code:""
missing_class:"QueryCondition"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.model.base.QueryCondition;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["38"]

问题36
error_type:"cannot find symbol"
error_code:""
missing_class:"BaseEntity"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.model.model.BaseEntity;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["39"]

问题37
error_type:"cannot find symbol"
error_code:""
missing_class:"ModelServiceUtils"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.model.tool.ModelServiceUtils;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["40"]

问题38
error_type:"cannot find symbol"
error_code:""
missing_class:"QueryConditionBuilder"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.model.tool.QueryConditionBuilder;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["41"]

问题39
error_type:"cannot find symbol"
error_code:""
missing_class:"Topology1Service"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.node.service.Topology1Service;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["42"]

问题40
error_type:"cannot find symbol"
error_code:""
missing_class:"TopologyCommonService"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.node.service.TopologyCommonService;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["43"]

问题41
error_type:"cannot find symbol"
error_code:""
missing_class:"QuantityAggregationDataDao"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.quantity.dao.QuantityAggregationDataDao;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["44"]

问题42
error_type:"cannot find symbol"
error_code:""
missing_class:"QuantityObjectDao"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.quantity.dao.QuantityObjectDao;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["45"]

问题43
error_type:"cannot find symbol"
error_code:""
missing_class:"QuantityDataBatchSearchVo"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["46"]

问题44
error_type:"cannot find symbol"
error_code:""
missing_class:"QuantitySearchVo"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.quantity.model.quantity.QuantitySearchVo;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["47"]

问题45
error_type:"cannot find symbol"
error_code:""
missing_class:"QuantityManageService"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.quantity.service.QuantityManageService;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["48"]

问题46
error_type:"cannot find symbol"
error_code:""
missing_class:"CollectionUtils"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.toolkit.CollectionUtils;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["49"]

问题47
error_type:"cannot find symbol"
error_code:""
missing_class:"PowerTransformerDao"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.transformer.dao.PowerTransformerDao;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["50"]

问题48
error_type:"cannot find symbol"
error_code:""
missing_class:"TransformerindexDataDao"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.transformer.dao.TransformerindexDataDao;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["51"]

问题49
error_type:"cannot find symbol"
error_code:""
missing_class:""
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.transformer.model.*;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["52"]

问题50
error_type:"cannot find symbol"
error_code:""
missing_class:"Constant"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.transformer.model.constant.Constant;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["53"]

问题51
error_type:"cannot find symbol"
error_code:""
missing_class:"EquipmentStatus"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.transformer.model.enums.EquipmentStatus;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["54"]

问题52
error_type:"cannot find symbol"
error_code:""
missing_class:"TransformerlevelEnum"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.transformer.model.enums.TransformerlevelEnum;"
calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["55"]

问题53
error_type:"cannot find symbol"
error_code:""
missing_class:"TransformerOverviewService"
missing_method: ""
missing_field:""
import_statements:
    -"import com.cet.eem.transformer.service.TransformerOverviewService;
"calling_class:"TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["56"]

问题54
error_type: "Topology1Service废弃"
error_code: ""
missing_class: "Topology1Service"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.node.service.Topology1Service;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["444"]

问题55
error_type: "QuantityObjectDao废弃"
error_code: ""
missing_class: "QuantityObjectDao"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.dao.QuantityObjectDao;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["282"]

问题56
error_type: "QuantityManageService废弃"
error_code: ""
missing_class: "QuantityManageService"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.service.QuantityManageService;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["693"]

问题57
error_type: "TopologyCommonService废弃"
error_code: ""
missing_class: "TopologyCommonService"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.node.service.TopologyCommonService;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["248"]

问题58
error_type: "PecEventService废弃"
error_code: ""
missing_class: "PecEventService"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.event.service.PecEventService;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["680"]

问题59
error_type: "ConnectionSearchVo废弃"
error_code: ""
missing_class: "ConnectionSearchVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.topology.vo.ConnectionSearchVo;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["242"]

问题60-63
error_type: "PecEventExtendVo废弃"
error_code: ""
missing_class: "PecEventExtendVo"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.bll.common.model.topology.vo.ConnectionSearchVo;"
calling_class: "TransformerOverviewServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["553","558","746","750"]
```
- TransformerTaskServiceImpl.java
```
问题1
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.bll.common.def.quantity.FrequencyDef;  "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["4"]

问题2
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.bll.common.def.quantity.PhasorDef; "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["5"]

问题3
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["6"]

问题4
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["7"]

问题5
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.bll.common.model.domain.object.organization.Project;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["8"]

问题6
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.common.CommonUtils;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["9"]

问题7
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.common.constant.EnergyTypeDef; "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["10"]

问题8
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.common.constant.EnumDataTypeId;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["11"]

问题9
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.common.constant.EnumOperationType; "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["12"]

问题10
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.common.definition.NodeLabelDef;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["13"]

问题11
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.common.model.datalog.DataLogData;  "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["14"]

问题12
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;  "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["15"]

问题13
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.common.utils.TimeUtil; "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["16"]

问题14
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.model.base.QueryCondition; "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["17"]

问题15
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.model.tool.ModelServiceUtils;  "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["18"]

问题16
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.model.tool.QueryConditionBuilder;  "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["19"]

问题17
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.node.service.Topology1Service; "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["20"]

问题18
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo; "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["21"]

问题19
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.quantity.model.quantity.QuantitySearchVo;  "
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["22"]

问题20
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.quantity.service.QuantityManageService;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["23"]

问题21
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.fusion.transformer.core.entity.bo.HistoricalLoadBO;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["24"]

问题22
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.toolkit.CollectionUtils;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["26"]

问题23
error_type:"cannot find symbol"
error_code:""
missing_class:""
miss_method:""
import_statements:
    -"import com.cet.eem.fusion.transformer.core.entity.dto.PowerTransformerDTO;"
calling_class:"TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["28"]

问题24-25
error_type: "QueryConditionBuilder废弃，需要用ParentQueryConditionBuilder重构"
error_code: "数据访问层变更详细方案"
missing_class: "QueryConditionBuilder"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.model.tool.QueryConditionBuilder;"
calling_class: "TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["84","93"]

问题24-25
error_type: "QuantityDataBatchSearchVo废弃"
error_code: "数据访问层变更详细方案"
missing_class: "QueryConditionBuilder"
missing_method: ""
missing_field: ""
import_statements:
    - "import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;"
calling_class: "TransformerTaskServiceImpl"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["102","132","133"]
```
- AvgloadTask.java
- TransformerTask.java
- DateUtil.java
```
问题1
error_type:"cannot find symbol"
error_code:""
missing_class:"AggregationCycle"
miss_method:""
import_statements:
    -"import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;"
calling_class:"DateUtil"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["3"]

问题2
error_type:"cannot find symbol"
error_code:""
missing_class:"TimeUtil"
miss_method:""
import_statements:
    -"import com.cet.eem.common.utils.TimeUtil;"
calling_class:"DateUtil"
calling_method: ""
old_dependency: ""
current_dependency: ""
usage_pattern: ""
parameter_types: []
return_type: ""
line:["4"]
```
- TransformerServiceApplication.java