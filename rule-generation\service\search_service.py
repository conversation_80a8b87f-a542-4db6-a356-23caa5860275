"""
搜索服务
封装代码检索相关的业务逻辑
"""

import os
import logging
from typing import List, Dict, Any

from utils.code_embedding import CodeEmbedding
from utils.file_utils import FileUtils
from utils.milvus_utils import MilvusUtils
from config.settings import SEARCH_CONFIG

logger = logging.getLogger(__name__)


class SearchService:
    """搜索服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.code_embedding = CodeEmbedding()
        self.milvus_utils = MilvusUtils()
        self.file_utils = FileUtils()
        self._collection_initialized = False
    
    def search_similar_code(self, query_file_path: str, k: int = None) -> Dict[str, Any]:
        """
        检索相似代码
        
        Args:
            query_file_path (str): 查询代码文件路径
            k (int): 返回结果数量，默认使用配置中的值
            
        Returns:
            Dict[str, Any]: 检索结果
        """
        if k is None:
            k = SEARCH_CONFIG["default_limit"]
        
        # 确保集合已初始化
        if not self._collection_initialized:
            logger.info("集合未初始化，正在初始化...")
            self.milvus_utils.create_collection(recreate=False)
            self._collection_initialized = True
        
        # 不在单个文件检索时输出详细信息，由批量检索统一管理输出
        
        try:
            # 1. 读取查询文件内容
            if not os.path.exists(query_file_path):
                raise FileNotFoundError(f"查询文件不存在: {query_file_path}")
            
            query_content = self.file_utils.read_file_content(query_file_path)
            if not query_content:
                raise ValueError("查询文件内容为空")
            
            # 2. 生成查询向量
            logger.info("生成查询向量...")
            query_embedding = self.code_embedding.get_code_embedding(query_content)
            query_vector = self.code_embedding.embedding_to_list(query_embedding)
            
            # 3. 执行搜索
            logger.info("执行向量搜索...")
            search_results = self.milvus_utils.search_vectors(query_vector, k)
            
            # 4. 处理结果
            processed_results = []
            for i, result in enumerate(search_results, 1):
                similarity_score = 1 / (1 + result['distance'])
                processed_result = {
                    "rank": i,
                    "id": result['id'],
                    "class_name": result['class_name'],
                    "distance": result['distance'],
                    "similarity_score": round(similarity_score, 4),
                    "code_content": result['code_content']
                }
                processed_results.append(processed_result)
                
                # 不在单个文件检索时输出详细结果，由批量检索统一管理
            
            return {
                "success": True,
                "query_file": query_file_path,
                "total_results": len(processed_results),
                "results": processed_results
            }
            
        except Exception as e:
            logger.error(f"代码检索失败: {e}")
            return {
                "success": False,
                "query_file": query_file_path,
                "error": str(e),
                "results": []
            }
    
    def batch_search_files(self, file_paths: List[str], k: int = None) -> Dict[str, Any]:
        """
        批量检索多个文件
        
        Args:
            file_paths (List[str]): 文件路径列表
            k (int): 每个文件返回的结果数量
            
        Returns:
            Dict[str, Any]: 批量检索结果
        """
        if k is None:
            k = SEARCH_CONFIG["default_limit"]
        
        logger.info(f"开始批量检索 {len(file_paths)} 个文件")
        
        batch_results = []
        successful_searches = 0
        failed_searches = 0
        
        for i, file_path in enumerate(file_paths, 1):
            # 显示当前检索进度
            filename = os.path.basename(file_path)
            logger.info(f"[{i}/{len(file_paths)}] 检索文件: {filename}")

            try:
                result = self.search_similar_code(file_path, k)
                if result["success"]:
                    successful_searches += 1
                    # 显示找到的匹配数量和详细结果
                    results = result["results"]
                    match_count = len([r for r in results if r.get("similarity_score", 0) >= 0.7])
                    logger.info(f"  └─ 找到 {len(results)} 个匹配，其中 {match_count} 个高相似度")

                    # 显示匹配结果列表（只显示高相似度的）
                    high_similarity_results = [r for r in results if r.get("similarity_score", 0) >= 0.7]
                    if high_similarity_results:
                        for i, res in enumerate(high_similarity_results[:3], 1):  # 最多显示3个
                            logger.info(f"      {i}. {res.get('class_name', 'N/A')} (相似度: {res.get('similarity_score', 0):.3f})")
                    else:
                        # 如果没有高相似度的，显示最高的一个
                        if results:
                            best_result = results[0]
                            logger.info(f"      1. {best_result.get('class_name', 'N/A')} (相似度: {best_result.get('similarity_score', 0):.3f})")
                else:
                    failed_searches += 1
                    logger.warning(f"  └─ 检索失败")

                batch_results.append(result)

            except Exception as e:
                logger.error(f"  └─ 检索文件失败: {e}")
                failed_searches += 1
                batch_results.append({
                    "success": False,
                    "query_file": file_path,
                    "error": str(e),
                    "results": []
                })
        
        logger.info(f"批量检索完成: 成功 {successful_searches}, 失败 {failed_searches}")
        
        return {
            "success": True,
            "total_files": len(file_paths),
            "successful_searches": successful_searches,
            "failed_searches": failed_searches,
            "batch_results": batch_results
        }
    
    def search_directory(self, directory_path: str, k: int = None, exclude_controller: bool = False) -> Dict[str, Any]:
        """
        检索目录下的所有Java文件

        Args:
            directory_path (str): 目录路径
            k (int): 每个文件返回的结果数量
            exclude_controller (bool): 是否忽略包含Controller的文件

        Returns:
            Dict[str, Any]: 目录检索结果
        """
        if not os.path.exists(directory_path):
            return {
                "success": False,
                "error": f"目录不存在: {directory_path}",
                "batch_results": []
            }
        
        # 扫描目录下的Java文件
        java_files = self.file_utils.scan_java_files(directory_path, exclude_controller=exclude_controller)
        
        if not java_files:
            return {
                "success": False,
                "error": f"目录下未找到Java文件: {directory_path}",
                "batch_results": []
            }
        
        logger.info(f"在目录 {directory_path} 中找到 {len(java_files)} 个Java文件")
        
        # 批量检索
        return self.batch_search_files(java_files, k)
    
    def cleanup(self):
        """清理资源"""
        try:
            self.milvus_utils.release_collection()
            logger.info("搜索服务资源清理完成")
        except Exception as e:
            logger.warning(f"搜索服务资源清理失败: {e}")
