{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "problems_count": 5, "analysis_results": [{"class_name": "LambdaQueryWrapper", "line": [3, 39, 68, 99], "description": "未解析的引用 LambdaQueryWrapper", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper", "com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper"], "suggest": "请使用新的类路径替换"}, {"class_name": "ModelDaoImpl", "line": [4, 20], "description": "未解析的引用 ModelDaoImpl", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl"], "suggest": "请使用新的类路径替换"}, {"class_name": "AggregationCycle", "line": [5], "description": "未解析的引用 AggregationCycle", "matched_class_paths": ["com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle", "com.cet.electric.baseconfig.sdk.common.def.AggregationCycle", "com.cet.electric.fusion.matrix.v2.utils.AggregationCycle"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyDao", "line": [6, 20], "description": "未解析的引用 TeamGroupEnergyDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergy", "line": [7, 20, 34, 39, 39, 64, 68, 68, 94, 99, 99], "description": "未解析的引用 TeamGroupEnergy", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy"], "suggest": "请使用新的类路径替换"}]}