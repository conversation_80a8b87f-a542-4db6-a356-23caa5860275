{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.controller", "problems_count": 18, "analysis_results": [{"class_name": "EnumAndOr", "line": [3], "description": "未解析的引用 EnumAndOr", "matched_class_paths": ["com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr"], "suggest": "请使用新的类路径替换"}, {"class_name": "OperationPermission", "line": [4, 41, 61, 69, 77, 92, 107, 122, 130, 138, 153], "description": "未解析的引用 OperationPermission", "matched_class_paths": ["com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission"], "suggest": "请使用新的类路径替换"}, {"class_name": "OperationLog", "line": [5, 40, 68, 76, 91, 106, 121, 129, 137, 152], "description": "未解析的引用 OperationLog", "matched_class_paths": ["com.cet.eem.fusion.config.sdk.service.log.OperationLog", "com.cet.electric.baseconfig.common.entity.OperationLog", "com.cet.electric.model.definition.OperationLog"], "suggest": "请使用新的类路径替换"}, {"class_name": "EnumOperationSubType", "line": [6], "description": "未解析的引用 EnumOperationSubType", "matched_class_paths": ["com.cet.eem.fusion.common.utils.EnumOperationSubType"], "suggest": "请使用新的类路径替换"}, {"class_name": "BaseVo", "line": [7, 99], "description": "未解析的引用 BaseVo", "matched_class_paths": ["com.cet.eem.fusion.common.model.BaseVo"], "suggest": "请使用新的类路径替换"}, {"class_name": "Result", "line": [8, 42, 54, 62, 78, 84, 93, 99, 108, 114, 131, 145, 154, 160, 171], "description": "未解析的引用 Result", "matched_class_paths": ["com.cet.eem.fusion.common.entity.Result", "com.cet.electric.matterhorn.cloud.authservice.common.entity.Result"], "suggest": "请使用新的类路径替换"}, {"class_name": "ResultWithTotal", "line": [9, 48], "description": "未解析的引用 ResultWithTotal", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "SchedulingSchemeAddUpdateDTO", "line": [42], "description": "未解析的引用 SchedulingSchemeAddUpdateDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeAddUpdateDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeDetailVO", "line": [48, 54, 62], "description": "未解析的引用 SchedulingSchemeDetailVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.SchedulingSchemeDetailVO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeQueryDTO", "line": [48], "description": "未解析的引用 SchedulingSchemeQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeRelatedHolidayDTO", "line": [78], "description": "未解析的引用 SchedulingSchemeRelatedHolidayDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeRelatedHolidayDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingSchemeRelatedNodeDTO", "line": [93], "description": "未解析的引用 SchedulingSchemeRelatedNodeDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeRelatedNodeDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesSchemeAddUpdateDTO", "line": [108], "description": "未解析的引用 ClassesSchemeAddUpdateDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesSchemeAddUpdateDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesSchemeVO", "line": [114], "description": "未解析的引用 ClassesSchemeVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesSchemeVO"], "suggest": "请添加新的import类导入"}, {"class_name": "TeamGroupInfoAddUpdateDTO", "line": [131], "description": "未解析的引用 TeamGroupInfoAddUpdateDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupInfoAddUpdateDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "TeamGroupInfoVO", "line": [145], "description": "未解析的引用 TeamGroupInfoVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupInfoVO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingClassesSaveDTO", "line": [154], "description": "未解析的引用 SchedulingClassesSaveDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingClassesSaveDTO"], "suggest": "请添加新的import类导入"}, {"class_name": "SchedulingClassesVO", "line": [160, 171], "description": "未解析的引用 SchedulingClassesVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.SchedulingClassesVO"], "suggest": "请添加新的import类导入"}]}