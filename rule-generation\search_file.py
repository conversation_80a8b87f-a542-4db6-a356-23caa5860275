"""
单文件检索脚本
专门用于单个文件的相似性检索
"""

import sys
import logging
import argparse
import os
from service.search_service import SearchService
from service.report_service import ReportService
from config.settings import SEARCH_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Java代码相似性检索工具')
    parser.add_argument(
        '--file', '-f',
        type=str,
        required=True,
        help='要检索的Java文件路径'
    )
    parser.add_argument(
        '--limit', '-k',
        type=int,
        default=3,
        help='返回结果数量 (默认: 3)'
    )
    parser.add_argument(
        '--output', '-o',
        type=str,
        help='输出报告文件名 (可选)'
    )
    parser.add_argument(
        '--no-report',
        action='store_true',
        help='不生成报告文件，只在控制台显示结果'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 验证输入文件
    if not os.path.exists(args.file):
        print(f"❌ 文件不存在: {args.file}")
        sys.exit(1)
    
    if not args.file.endswith('.java'):
        print(f"⚠️  警告: 文件不是Java文件: {args.file}")
    
    # 显示配置信息
    print("=" * 60)
    print("Java代码相似性检索工具")
    print("=" * 60)
    print(f"查询文件: {args.file}")
    print(f"返回结果数: {args.limit}")
    print(f"生成报告: {'否' if args.no_report else '是'}")
    print(f"详细输出: {'是' if args.verbose else '否'}")
    print("=" * 60)
    
    # 初始化服务
    search_service = SearchService()
    
    try:
        # 执行检索
        result = search_service.search_similar_code(args.file, args.limit)
        
        # 显示结果
        if result["success"]:
            print("\n✅ 检索成功！")
            print(f"找到 {result['total_results']} 个相似代码\n")
            
            # 在控制台显示简要结果
            for res in result["results"]:
                print(f"排名 {res['rank']}: {res['class_name']}")
                print(f"  文件: {res['file_path']}")
                print(f"  相似度: {res['similarity_score']:.4f}")
                print(f"  距离: {res['distance']:.4f}")
                print("-" * 40)
            
            # 生成报告
            if not args.no_report:
                report_service = ReportService()
                report_path = report_service.generate_search_report(
                    result, 
                    args.output
                )
                print(f"\n📄 报告已生成: {report_path}")
            
        else:
            print(f"\n❌ 检索失败: {result.get('error', '未知错误')}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"检索过程中发生异常: {e}")
        print(f"\n❌ 检索失败: {e}")
        sys.exit(1)
    finally:
        search_service.cleanup()


if __name__ == "__main__":
    main()
