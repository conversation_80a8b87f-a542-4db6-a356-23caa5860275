{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "problems_count": 4, "analysis_results": [{"class_name": "LambdaQueryWrapper", "line": [3, 27], "description": "未解析的引用 LambdaQueryWrapper", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper", "com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper"], "suggest": "请使用新的类路径替换"}, {"class_name": "ModelDaoImpl", "line": [4, 17], "description": "未解析的引用 ModelDaoImpl", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingSchemeToNodeDao", "line": [5, 17], "description": "未解析的引用 SchedulingSchemeToNodeDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeToNodeDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingSchemeToNode", "line": [6, 17, 26, 27, 27], "description": "未解析的引用 SchedulingSchemeToNode", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode"], "suggest": "请使用新的类路径替换"}]}