package com.cet.eem.fusion.groupenergy.core.dao;

import com.cet.eem.dao.BaseModelDao;
import com.cet.piem.entity.classes.SchedulingSchemeToNode;

import java.util.List;

/**
 * 方案关联节点dao
 *
 * <AUTHOR>
 */
public interface SchedulingSchemeToNodeDao extends BaseModelDao<SchedulingSchemeToNode> {

    /**
     * 查询排班方案关联节点
     *
     * @param schedulingSchemeId 排班方案id
     * @return 关联节点
     */
    List<SchedulingSchemeToNode> queryBySchedulingSchemeId(Long schedulingSchemeId);
}
