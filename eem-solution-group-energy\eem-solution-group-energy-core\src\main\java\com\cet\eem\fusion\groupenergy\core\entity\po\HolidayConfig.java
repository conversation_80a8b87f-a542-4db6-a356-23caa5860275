package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.model.model.BaseEntity;
import com.cet.piem.common.constant.TableNameDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 节假日信息表
 *
 * <AUTHOR>
 * @date 2024/12/20 14:30
 */
@Data
@AllArgsConstructor
@ModelLabel(TableNameDef.HOLIDAY_CONFIG)
public class HolidayConfig extends BaseEntity {
    @ApiModelProperty("排班方案表")
    @JsonProperty("schedulingschemeid")
    private Long schedulingSchemeId;

    @ApiModelProperty("节假日具体日期")
    @JsonProperty("holidaydate")
    private Long holidayDate;

    public HolidayConfig() {
        this.modelLabel = TableNameDef.HOLIDAY_CONFIG;
    }
}
