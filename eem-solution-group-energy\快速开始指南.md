# Java导入问题分析工具 - 快速开始指南

## 🚀 5分钟快速上手

### 前置条件
- ✅ Python 3.6+
- ✅ Maven 3.x
- ✅ 包含QodanaJavaSanity.xml的Java项目

### 步骤1: 分割问题文件 (30秒)
```bash
python problem_splitter.py eem-solution-group-energy-core/QodanaJavaSanity.xml
```
**结果**: 在`split_problems/`目录生成按文件分组的问题文件

### 步骤2: 分析单个文件 (2-3分钟)
```bash
python precise_class_finder.py "split_problems/eem-solution-group-energy-core_src_main_java_com_cet_eem_fusion_groupenergy_core_controller_TeamConfigController_problems.xml" "eem-solution-group-energy-core" "E:\maven\apache-maven-3.6.3\bin\mvn.cmd"
```
**结果**: 在`split_analysis/`目录生成JSON分析报告

### 步骤3: 批量分析所有文件 (可选)
```bash
# Windows
batch_analyze.bat

# Linux/Mac
chmod +x batch_analyze.sh
./batch_analyze.sh
```

## 📁 输出目录结构
```
项目根目录/
├── split_problems/          # 步骤1输出：分割后的问题文件
│   ├── Controller1_problems.xml
│   └── Controller2_problems.xml
├── split_analysis/          # 步骤2输出：分析报告
│   ├── Controller1_analysis_report.json
│   └── Controller2_analysis_report.json
└── 工具文件/
    ├── problem_splitter.py
    ├── precise_class_finder.py
    └── batch_analyze.bat
```

## 🔧 修复Java文件

根据JSON报告中的`matched_class_paths`更新import语句：

**修复前**:
```java
import com.cet.eem.auth.aspect.EnumAndOr;  // ❌ 错误路径
```

**修复后**:
```java
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;  // ✅ 正确路径
```

## 📊 分析报告示例

```json
{
  "problems_count": 7,
  "analysis_results": [
    {
      "class_name": "EnumAndOr",
      "line": 3,
      "matched_class_paths": [
        "com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr"
      ],
      "suggest": "请使用新的类路径替换"
    }
  ]
}
```

## ⚡ 常用命令

### 检查Maven是否可用
```bash
mvn --version
```

### 手动构建classpath（调试用）
```bash
cd eem-solution-group-energy-core
mvn dependency:build-classpath -Dmdep.outputFile=classpath.txt
```

### 查看生成的文件
```bash
# Windows
dir split_problems
dir split_analysis

# Linux/Mac
ls split_problems/
ls split_analysis/
```

## 🆘 快速故障排除

| 问题 | 解决方案 |
|------|----------|
| `系统找不到指定的文件` | 指定Maven完整路径 |
| `classpath.txt文件未生成` | 检查网络连接和pom.xml配置 |
| `找不到任何类` | 确认项目依赖已下载 |
| `编码错误` | 确保XML文件为UTF-8编码 |

## 📞 需要帮助？

1. 查看完整的 `README_工具使用说明.md`
2. 检查控制台输出的错误信息
3. 确认环境配置是否正确

---

**🎯 目标**: 5分钟内完成第一个文件的分析，获得可执行的修复建议！