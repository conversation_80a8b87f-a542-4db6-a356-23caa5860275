{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "problems_count": 3, "analysis_results": [{"class_name": "ModelDaoImpl", "line": [3, 14], "description": "未解析的引用 ModelDaoImpl", "matched_class_paths": ["com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl"], "suggest": "请使用新的类路径替换"}, {"class_name": "ClassesConfigDao", "line": [4, 14], "description": "未解析的引用 ClassesConfigDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.ClassesConfigDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "ClassesConfig", "line": [5, 14], "description": "未解析的引用 ClassesConfig", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig"], "suggest": "请使用新的类路径替换"}]}