package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.model.model.BaseEntity;
import com.cet.piem.common.constant.TableNameDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 班次配置表
 *
 * <AUTHOR>
 * @date 2024/12/20 14:26
 */
@Data
@AllArgsConstructor
@ModelLabel(TableNameDef.CLASSES_CONFIG)
public class ClassesConfig extends BaseEntity {
    @ApiModelProperty("开始时间")
    @JsonProperty("starttime")
    private Long startTime;

    @ApiModelProperty("结束时间")
    @JsonProperty("endtime")
    private Long endTime;

    @ApiModelProperty("次序")
    @JsonProperty("serialnumber")
    private Integer serialNumber;

    public ClassesConfig() {
        this.modelLabel = TableNameDef.CLASSES_CONFIG;
    }
}
