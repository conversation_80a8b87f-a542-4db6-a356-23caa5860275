package com.cet.eem.fusion.groupenergy.core.service;


import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.ResultWithTotal;
import com.cet.piem.entity.dto.classes.*;
import com.cet.piem.entity.vo.classes.*;

import java.util.List;

/**
 * 班组配置业务层
 *
 * <AUTHOR>
 */
public interface TeamConfigService {
    /**
     * 新增修改排班方案
     *
     * @param dto 排班方案
     * @return Boolean
     */
    Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto);

    /**
     * 查询排班方案
     *
     * @param dto 查询条件
     * @return 排班方案
     */
    ResultWithTotal<List<SchedulingSchemeDetailVO>> querySchedulingScheme(SchedulingSchemeQueryDTO dto);

    /**
     * 查询所有排班方案
     *
     * @return 排班方案详情
     */
    List<SchedulingSchemeDetailVO> allSchedulingScheme();


    /**
     * 删除方案
     *
     * @param id 排班方案id
     * @return Boolean
     */
    void deleteSchedulingScheme(Long id);

    /**
     * 排班方案关联节假日
     *
     * @param dto 排班方案假期关联关系
     * @return Boolean
     */
    Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto);

    /**
     * 查询排班方案关联节假日
     *
     * @param schedulingSchemeId 排班方案id
     * @return 关联节假日
     */
    List<Long> querySchedulingSchemeRelatedHoliday(Long schedulingSchemeId);

    /**
     * 保存排班方案关联节点
     *
     * @param dto 方案关联节点
     * @return Boolean
     */
    Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto);

    /**
     * 查询排班方案关联节点
     *
     * @param schedulingSchemeId 排版方案id
     * @return 排班方案关联节点
     */
    List<BaseVo> querySchedulingSchemeRelatedNode(Long schedulingSchemeId);

    /**
     * 新增修改班次方案
     *
     * @param dto 班次方案
     * @return Boolean
     */
    Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto);

    /**
     * 查询班次方案
     *
     * @param id 排班方案id
     * @return 班次方案
     */
    List<ClassesSchemeVO> queryClassesScheme(Long id);

    /**
     * 删除班次方案以及班次
     *
     * @param id 班次方案
     * @return Boolean
     */
    void deleteClassesScheme(Long id);

    /**
     * 新增修改班组
     *
     * @param dto 班组
     * @return Boolean
     */
    Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto);

    /**
     * 删除班组
     *
     * @param id 班组id
     * @return Boolean
     */
    void deleteTeamGroupInfo(Long id);

    /**
     * 查询班组
     *
     * @param schedulingSchemeId 排班方案id
     * @return 班组
     */
    List<TeamGroupInfoVO> queryTeamGroupInfo(Long schedulingSchemeId);

    /**
     * 保存排班表
     *
     * @param dto 排班表
     * @return Boolean
     */
    Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto);

    /**
     * 查询排班表
     *
     * @param starTime 开始时间
     * @param endTime  结束时间
     * @return 排班表
     */
    List<SchedulingClassesVO> querySchedulingClasses(Long starTime, Long endTime,Long schedulingSchemeId);


    /**
     * 查询时间范围内排班班组配置
     *
     * @param starTime 开始时间
     * @param endTime  结束时间
     * @return 班组配置
     */
    List<SchedulingClassesVO> querySchedulingClassesTeamGroupInfo(Long starTime, Long endTime,Long schedulingSchemeId);

    /**
     * 查询生产类型排班方案
     *
     * @return 生产类型排班方案
     */
    List<SchedulingSchemeDetailVO> queryProduceSchedulingSchemeByType(Integer classTeamType);

}
