<?xml version='1.0' encoding='cp936'?>
<problems><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ParamUtils</description>
  <highlighted_element>ParamUtils</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>10</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 exception</description>
  <highlighted_element>exception</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>9</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>36</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Result</description>
  <highlighted_element>Result</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 model</description>
  <highlighted_element>model</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 auth</description>
  <highlighted_element>auth</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 user</description>
  <highlighted_element>user</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserVo</description>
  <highlighted_element>UserVo</highlighted_element>
  <language>JAVA</language>
  <offset>42</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 parse</description>
  <highlighted_element>parse</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 JsonTransferUtils</description>
  <highlighted_element>JsonTransferUtils</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 common</description>
  <highlighted_element>common</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 utils</description>
  <highlighted_element>utils</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>5</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TimeUtil</description>
  <highlighted_element>TimeUtil</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>8</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 EemCloudAuthService</description>
  <highlighted_element>EemCloudAuthService</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>16</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>16</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 teamenergy</description>
  <highlighted_element>teamenergy</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>10</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>16</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>16</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamConfigService</description>
  <highlighted_element>TeamConfigService</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>34</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamConfigService</description>
  <highlighted_element>TeamConfigService</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl schedulingSchemeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>40</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl holidayConfigDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfigDao</description>
  <highlighted_element>HolidayConfigDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>43</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl schedulingSchemeToNodeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>46</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl classesSchemeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeDao</description>
  <highlighted_element>ClassesSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>49</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl classesConfigDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDao</description>
  <highlighted_element>ClassesConfigDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>52</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl schedulingClassesDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesDao</description>
  <highlighted_element>SchedulingClassesDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>20</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl teamGroupInfoDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoDao</description>
  <highlighted_element>TeamGroupInfoDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>58</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl cloudAuthService" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 EemCloudAuthService</description>
  <highlighted_element>EemCloudAuthService</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>68</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeAddUpdateDTO</description>
  <highlighted_element>SchedulingSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>28</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>70</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>80</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>22</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>83</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>83</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>95</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>95</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>95</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>97</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>97</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>29</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>98</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>99</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>99</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>29</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>99</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>69</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>113</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; allSchedulingScheme()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>114</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; allSchedulingScheme()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>130</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>142</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>144</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>151</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>160</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>162</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>168</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>195</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedHolidayDTO</description>
  <highlighted_element>SchedulingSchemeRelatedHolidayDTO</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>33</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>198</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>207</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>209</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>209</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>226</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;java.lang.Long&gt; querySchedulingSchemeRelatedHoliday(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>241</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedNodeDTO</description>
  <highlighted_element>SchedulingSchemeRelatedNodeDTO</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>30</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>243</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>253</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>254</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>255</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>256</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>256</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>276</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;BaseVo&gt; querySchedulingSchemeRelatedNode(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>277</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;BaseVo&gt; querySchedulingSchemeRelatedNode(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>281</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;BaseVo&gt; querySchedulingSchemeRelatedNode(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BaseVo</description>
  <highlighted_element>BaseVo</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>293</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeAddUpdateDTO</description>
  <highlighted_element>ClassesSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>294</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>296</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>297</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>301</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>22</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>306</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>22</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>309</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>315</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>323</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>324</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>325</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>325</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>334</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>334</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>344</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>348</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>352</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>358</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>362</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>364</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>370</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>371</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>372</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>372</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>394</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>396</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>404</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>405</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>406</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>406</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>420</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>431</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>433</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>442</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoAddUpdateDTO</description>
  <highlighted_element>TeamGroupInfoAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>446</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>451</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>454</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>458</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>458</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>467</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>469</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>475</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>479</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>483</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>483</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>504</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteTeamGroupInfo(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>506</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteTeamGroupInfo(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 BusinessBaseException</description>
  <highlighted_element>BusinessBaseException</highlighted_element>
  <language>JAVA</language>
  <offset>22</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>518</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>519</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>520</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>525</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>526</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>526</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>535</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Result</description>
  <highlighted_element>Result</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>535</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserVo</description>
  <highlighted_element>UserVo</highlighted_element>
  <language>JAVA</language>
  <offset>28</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>538</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserVo</description>
  <highlighted_element>UserVo</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>542</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UserVo</description>
  <highlighted_element>UserVo</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>571</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesSaveDTO</description>
  <highlighted_element>SchedulingClassesSaveDTO</highlighted_element>
  <language>JAVA</language>
  <offset>41</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>573</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>579</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>580</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigDTO</description>
  <highlighted_element>SchedulingClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>26</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>581</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>581</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>602</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>608</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>619</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>622</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>623</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>625</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>626</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>627</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>627</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>41</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>630</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>631</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>632</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>632</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>634</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>644</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>672</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>679</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>694</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>697</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>698</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>700</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>701</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>702</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>702</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>41</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>705</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>706</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>707</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>707</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>709</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>719</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>749</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; queryProduceSchedulingSchemeByType(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>750</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; queryProduceSchedulingSchemeByType(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>765</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>765</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>77</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>766</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>767</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>768</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>768</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>776</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>777</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>778</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>778</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>785</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>786</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>787</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>787</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>15</length>
</problem>
</problems>