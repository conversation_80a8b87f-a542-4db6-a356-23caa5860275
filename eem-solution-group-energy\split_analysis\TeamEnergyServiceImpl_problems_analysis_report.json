{"file": "file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "problems_count": 29, "analysis_results": [{"class_name": "ProjectUnitClassify", "line": [3], "description": "未解析的引用 ProjectUnitClassify", "matched_class_paths": ["com.cet.eem.fusion.common.def.base.ProjectUnitClassify"], "suggest": "请使用新的类路径替换"}, {"class_name": "UserDefineUnit", "line": [4, 88, 186, 257, 302, 404], "description": "未解析的引用 UserDefineUnit", "matched_class_paths": ["com.cet.electric.baseconfig.common.entity.UserDefineUnit"], "suggest": "请使用新的类路径替换"}, {"class_name": "CommonUtils", "line": [5], "description": "未解析的引用 CommonUtils", "matched_class_paths": ["com.cet.eem.fusion.common.utils.CommonUtils", "com.cet.electric.matterhorn.devicedataservice.common.utils.CommonUtils"], "suggest": "请使用新的类路径替换"}, {"class_name": "EnumOperationType", "line": [6], "description": "未解析的引用 EnumOperationType", "matched_class_paths": ["com.cet.eem.fusion.common.def.common.EnumOperationType"], "suggest": "请使用新的类路径替换"}, {"class_name": "ColumnDef", "line": [7], "description": "未解析的引用 ColumnDef", "matched_class_paths": ["com.cet.eem.fusion.common.def.common.ColumnDef"], "suggest": "请使用新的类路径替换"}, {"class_name": "BaseVo", "line": [8, 469, 469, 481, 497, 497], "description": "未解析的引用 BaseVo", "matched_class_paths": ["com.cet.eem.fusion.common.model.BaseVo"], "suggest": "请使用新的类路径替换"}, {"class_name": "TimeUtil", "line": [9], "description": "未解析的引用 TimeUtil", "matched_class_paths": ["com.cet.eem.fusion.common.utils.time.TimeUtil", "com.cet.electric.baseconfig.sdk.common.utils.TimeUtil", "com.cet.electric.fusion.matrix.v2.utils.TimeUtil"], "suggest": "请使用新的类路径替换"}, {"class_name": "TableColumnNameDef", "line": [10], "description": "未解析的引用 TableColumnNameDef", "matched_class_paths": ["com.cet.eem.solution.common.def.common.label.TableColumnNameDef"], "suggest": "请使用新的类路径替换"}, {"class_name": "AggregationCycle", "line": [11], "description": "未解析的引用 AggregationCycle", "matched_class_paths": ["com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle", "com.cet.electric.baseconfig.sdk.common.def.AggregationCycle", "com.cet.electric.fusion.matrix.v2.utils.AggregationCycle"], "suggest": "请使用新的类路径替换"}, {"class_name": "DoubleUtils", "line": [12], "description": "未解析的引用 DoubleUtils", "matched_class_paths": ["com.cet.eem.solution.common.utils.DoubleUtils"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingSchemeDao", "line": [13], "description": "未解析的引用 SchedulingSchemeDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingSchemeToNodeDao", "line": [14], "description": "未解析的引用 SchedulingSchemeToNodeDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeToNodeDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyDao", "line": [15], "description": "未解析的引用 TeamGroupEnergyDao", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao"], "suggest": "请使用新的类路径替换"}, {"class_name": "ClassesEnergyInfoQueryDTO", "line": [17, 372], "description": "未解析的引用 ClassesEnergyInfoQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyInfoQueryDTO", "line": [18, 65, 153, 271], "description": "未解析的引用 TeamGroupEnergyInfoQueryDTO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO"], "suggest": "请使用新的类路径替换"}, {"class_name": "ClassesEnergyInfoVO", "line": [19, 271, 272, 310, 310, 319, 319, 372, 373, 373, 436, 446, 446], "description": "未解析的引用 ClassesEnergyInfoVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyCard", "line": [20, 101, 103, 103], "description": "未解析的引用 TeamGroupEnergyCard", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyCard"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyHistogramVO", "line": [21, 153, 190, 193, 193, 204, 217, 217, 232, 232], "description": "未解析的引用 TeamGroupEnergyHistogramVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamGroupEnergyInfoVO", "line": [22, 65, 67, 67], "description": "未解析的引用 TeamGroupEnergyInfoVO", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO"], "suggest": "请使用新的类路径替换"}, {"class_name": "UnitService", "line": [23], "description": "未解析的引用 UnitService", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "NodeServiceImpl", "line": [24], "description": "未解析的引用 NodeServiceImpl", "matched_class_paths": ["com.cet.electric.baseconfig.sdk.service.impl.NodeServiceImpl"], "suggest": "请使用新的类路径替换"}, {"class_name": "TeamEnergyService", "line": [25, 41], "description": "未解析的引用 TeamEnergyService", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService"], "suggest": "请使用新的类路径替换"}, {"class_name": "SchedulingScheme", "line": [70, 161, 275, 376], "description": "未解析的引用 SchedulingScheme", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme"], "suggest": "请添加新的import类导入"}, {"class_name": "TeamGroupInfo", "line": [76, 108, 167, 211, 427], "description": "未解析的引用 TeamGroupInfo", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo"], "suggest": "请添加新的import类导入"}, {"class_name": "TeamGroupEnergy", "line": [80, 98, 102, 106, 178, 199, 204, 205, 208, 216, 217, 217, 232, 232, 294, 305, 316, 396, 407], "description": "未解析的引用 TeamGroupEnergy", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesConfig", "line": [170, 219, 281, 320, 382, 441], "description": "未解析的引用 ClassesConfig", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesScheme", "line": [282, 383, 437], "description": "未解析的引用 ClassesScheme", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme"], "suggest": "请添加新的import类导入"}, {"class_name": "ClassesName", "line": [319, 319, 436, 446, 446], "description": "未解析的引用 ClassesName", "matched_class_paths": [], "suggest": "类已经废弃"}, {"class_name": "SchedulingSchemeToNode", "line": [468], "description": "未解析的引用 SchedulingSchemeToNode", "matched_class_paths": ["com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode"], "suggest": "请添加新的import类导入"}]}