# 代码检索工具梳理

## 项目概述

基于CodeBERT和Milvus的Java代码向量检索工具，用于代码相似性搜索和检索。

## 核心功能

### 1. 代码向量化
- 使用CodeBERT模型将Java代码转换为768维向量
- 支持批量处理Java文件
- 自动提取类名和文件信息

### 2. 向量存储
- 使用Milvus向量数据库存储代码向量
- 支持大规模向量检索
- 自动创建索引优化查询性能

### 3. 相似性检索
- 基于向量相似度进行代码检索
- 支持自定义返回结果数量
- 提供相似度分数计算

## 技术架构

### 模型层
- **CodeBERT**: microsoft/codebert-base
- **向量维度**: 768维
- **最大输入长度**: 512 tokens

### 存储层
- **向量数据库**: Milvus
- **索引类型**: IVF_FLAT
- **距离度量**: L2欧氏距离

### 应用层
- **主程序**: main.py
- **工具类**: utils/目录下的各种工具
- **配置管理**: config.py

## 数据结构

### 向量记录结构
```
{
    "id": 自增主键,
    "class_name": Java类名,
    "file_path": 文件相对路径,
    "code_content": 代码内容(智能截断),
    "code_vector": 768维向量
}
```

## 核心流程

### 初始化流程
1. 连接Milvus数据库
2. 创建或获取集合
3. 扫描Java文件
4. 批量读取文件内容
5. 提取类名信息
6. 生成代码向量
7. 插入向量数据库
8. 创建索引

### 检索流程
1. 读取查询文件
2. 生成查询向量
3. 在向量库中搜索
4. 计算相似度分数
5. 返回排序结果

## 配置参数

### Milvus配置
- 服务器地址: 10.12.140.76
- 端口: 19530
- 集合名称: java_code_vectors

### 文件处理配置
- 最大文件大小: 2MB
- 大文件阈值: 500KB
- 最大内容长度: 500,000字符
- 支持编码: UTF-8, GBK

### 搜索配置
- 默认返回数量: 5
- 索引参数: nlist=128
- 搜索参数: nprobe=10

## 大文件处理策略

### 文件分类
- **小文件** (<500KB): 正常处理
- **大文件** (500KB-2MB): 智能截断
- **超大文件** (>2MB): 跳过处理

### 智能截断
- 在方法或类边界截断
- 保留代码结构完整性
- 添加截断标记
- 显示原文件长度信息

## 工具集合

### 主要工具
- **main.py**: 主程序入口
- **test_search.py**: 检索功能测试
- **handle_large_files.py**: 大文件分析工具
- **fix_collection.py**: 集合修复工具

### 辅助工具
- **usage_example.py**: 使用示例
- **test_large_files.py**: 大文件处理测试

## 性能特点

### 优势
- 语义理解: 基于CodeBERT的深度语义理解
- 高效检索: Milvus向量数据库高性能检索
- 智能处理: 自动处理各种大小的文件
- 易于使用: 简洁的API接口

### 限制
- 模型限制: 输入长度限制512 tokens
- 语言限制: 专门针对Java优化
- 资源需求: 需要一定的计算和存储资源

## 使用场景

### 适用场景
- 代码重构时查找相似代码
- 学习项目中的设计模式
- 查找功能相似的类和方法
- 代码审查和质量分析

### 典型用例
- 查找所有的实体类
- 寻找相似的业务逻辑
- 发现重复的代码模式
- 分析代码结构相似性

## 扩展方向

### 功能扩展
- 支持其他编程语言
- 增加代码片段级别检索
- 集成IDE插件
- 提供Web界面

### 性能优化
- 模型量化加速
- 分布式向量存储
- 缓存机制优化
- 批量检索优化

## 维护说明

### 日常维护
- 定期备份向量数据
- 监控Milvus服务状态
- 更新CodeBERT模型
- 清理临时文件

### 故障处理
- 集合损坏修复
- 大文件处理异常
- 向量维度不匹配
- 内存不足问题

## 版本历史

### 当前版本特性
- 支持500K字符的大文件
- 智能截断算法
- 完善的错误处理
- 丰富的工具集合

### 已解决问题
- 字段长度超限问题
- 大文件处理异常
- 集合初始化错误
- 统计信息获取失败

## 总结

这是一个功能完整、性能优良的Java代码检索工具，具有以下特点：

1. **智能化**: 基于深度学习的语义理解
2. **高效性**: 向量数据库快速检索
3. **健壮性**: 完善的错误处理和恢复机制
4. **易用性**: 简洁的API和丰富的工具
5. **扩展性**: 模块化设计便于功能扩展

工具已成功处理475个Java文件，可以投入实际使用。







## 实现目标

输入项目重构前后的文件地址：根据相关java文件内容，整理输出相关的迁移前后规则。
输出规则包括：

- 前后项目替换方案：导入路径
- 方法替换方案
- 废弃类和废弃方法名单。

## 工程结构化

识别文件范围：实体类、Service、工具类、feign
结构化数据内容：

## 静态匹配

名称匹配
方法匹配
属性匹配

## 向量匹配（核心）

通过静态代码分析提取新工程的以下特征值，用于描述每个类的功能和结构

- 基本信息：
  - 类名、包名。
  - 方法名和参数列表。
  - 字段（变量）名称和类型。
- 注解信息：
  - 引用的外部类、库和 API。
  - 使用的注解（如 `@Service`、`@Controller` 等）。
- 代码结构：
  - 抽象类、接口、继承链信息。

基于老的工程，提取相关特征值，进行检索，提取相关可能匹配到的类或者方法。

最后基于ai做出相关判断

## 汇总输出规则

静态匹配和向量匹配方案可以作为不同权重的分数参考，这部分还需要完善相关评分规则

将整体的内容整理输出为相关中文规则，方便进行人为确认相关规则正确性

## rules生成

拆分相关规则，分批让ai基于提示词输出最终的rules文件，供监测工具使用



拓展应用：
1、针对不同业务线，针对现存方法的使用说明，可能之前需要依赖询问其他人，现在可以直接基于向量库做匹配
2、拓展：不同业务线使用不同的collection做区分，完全隔离各个业务线的规范