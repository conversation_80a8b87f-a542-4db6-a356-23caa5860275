@echo off
chcp 65001 >nul
echo ==========================================
echo Java导入问题批量分析工具
echo ==========================================
echo.

REM 设置Maven路径（请根据实际情况修改）
set MAVEN_PATH=E:\maven\apache-maven-3.6.3\bin\mvn.cmd

REM 设置项目路径
set PROJECT_PATH=eem-solution-group-energy-core

echo 配置信息:
echo Maven路径: %MAVEN_PATH%
echo 项目路径: %PROJECT_PATH%
echo.

REM 检查split_problems目录是否存在
if not exist "split_problems" (
    echo 错误: split_problems目录不存在
    echo 请先运行problem_splitter.py分割问题文件
    pause
    exit /b 1
)

echo 开始批量分析split_problems目录下的问题文件...
echo.

REM 统计文件数量
set count=0
for %%f in (split_problems\*_problems.xml) do set /a count+=1

if %count%==0 (
    echo 警告: split_problems目录下没有找到问题文件
    pause
    exit /b 1
)

echo 找到 %count% 个问题文件，开始分析...
echo.

REM 分析每个问题文件
set processed=0
for %%f in (split_problems\*_problems.xml) do (
    set /a processed+=1
    echo [!processed!/%count%] 正在分析: %%~nxf
    python precise_class_finder.py "%%f" "%PROJECT_PATH%" "%MAVEN_PATH%"
    echo.
    echo ----------------------------------------
    echo.
)

echo ==========================================
echo 批量分析完成！
echo 处理了 %processed% 个文件
echo 分析报告已保存到 split_analysis 目录
echo ==========================================
pause