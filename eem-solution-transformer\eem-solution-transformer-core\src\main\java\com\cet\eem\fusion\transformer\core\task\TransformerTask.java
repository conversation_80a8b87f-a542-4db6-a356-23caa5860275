package com.cet.eem.fusion.transformer.core.task;

import com.cet.eem.fusion.transformer.core.service.TransformerTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Configuration
@EnableScheduling
public class TransformerTask {

    @Autowired
    TransformerTaskService transformerTaskService;

    @Scheduled(cron = "${cet.eem.transformeranalysis.cron:-}")
    public void calculate() throws InstantiationException, IllegalAccessException {
        transformerTaskService.historicalLoadCalculation(System.currentTimeMillis());
    }
}
